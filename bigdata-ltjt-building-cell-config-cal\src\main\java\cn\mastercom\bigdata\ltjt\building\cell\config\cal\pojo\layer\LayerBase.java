package cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.layer;

import cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils.SourceUtils;
import cn.mastercom.mtcommon.gis.GisConverter;
import cn.mastercom.mtcommon.gis.booster.FindDBooster;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

public abstract class LayerBase<T> implements Serializable {
    private static final Logger logger = LoggerFactory.getLogger(LayerBase.class);
    private final FindDBooster<T> booster;
    private final String name;
    private int size;

    protected abstract T fromRow(Row row);

    protected abstract List<T> doIntersects(List<T> list, double d, double d2);

    protected abstract List<T> doIntersects(List<T> list, double d, double d2, double d3, double d4);

    protected abstract boolean valid(T t);

    protected LayerBase(FindDBooster<T> booster, String name) {
        this.booster = booster;
        this.name = name;
    }

    public boolean valid() {
        return this.booster != null && !this.booster.isEmpty();
    }

    public void initFromHive(SparkSession sparkSession, String sql) {
        logger.info("=== begin reading {} layer from hive, sql is:{} ===", this.name, sql);
        JavaRDD<Row> rawDataRDD = SourceUtils.read(sparkSession, sql, "building-layer");
        if (null == rawDataRDD) {
            return;
        }

        List<T> ts = rawDataRDD
                .map(this::fromRow)
                .filter(Objects::nonNull)
                .filter(this::valid)
                .collect();
        for (T t : ts) {
            this.booster.addItem(t);
        }
        this.size = ts.size();
        logger.info("=== {} {} features have been read. ===", ts.size(), this.name);
        logger.info("=== end reading {} layer from hive ===", this.name);
    }

    public List<T> find(int longitude, int latitude) {
        double lon = GisConverter.i2d(longitude);
        double lat = GisConverter.i2d(latitude);
        return find(lon, lat);
    }

    public List<T> find(int tlLongitude, int tlLatitude, int brLongitude, int brLatitude) {
        double tlLon = GisConverter.i2d(tlLongitude);
        double tlLat = GisConverter.i2d(tlLatitude);
        double brLon = GisConverter.i2d(brLongitude);
        double brLat = GisConverter.i2d(brLatitude);
        return find(tlLon, tlLat, brLon, brLat);
    }

    public List<T> find(double longitude, double latitude) {
        List<T> ls = this.booster.findItems(longitude, latitude);
        return doIntersects(ls, longitude, latitude);
    }

    public List<T> find(double tlLongitude, double tlLatitude, double brLongitude, double brLatitude) {
        List<T> ls = this.booster.findItems(tlLongitude, brLatitude, brLongitude, tlLatitude);
        return doIntersects(ls, tlLongitude, tlLatitude, brLongitude, brLatitude);
    }

    public int getSize() {
        return this.size;
    }

    public void setSize(int size) {
        this.size = size;
    }
}