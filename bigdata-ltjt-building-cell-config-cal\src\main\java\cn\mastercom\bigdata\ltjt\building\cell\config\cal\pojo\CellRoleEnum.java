package cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo;

import scala.Serializable;

public enum CellRoleEnum implements Serializable {

    /**
     * 主室分小区
     */
    MAIN_INDOOR_CELL("mainIndoorCell"),

    /**
     * 辅室分小区
     */
    AUXILIARY_INDOOR_CELL("auxiliaryIndoorCell"),

    /**
     * 备室分小区(保留室分小区)
     */
    ALTERNATIVE_AUXILIARY_INDOOR_CELL("alternativeAuxiliaryIndoorCell"),

    /**
     * 其他小区-A
     */
    OTHER_CELL_A("otherCell-A"),

    /**
     * 其他小区-B
     */
    OTHER_CELL_B("otherCell-B"),

    /**
     * 其他小区-C
     */
    OTHER_CELL_C("otherCell-C"),

    /**
     * 其他小区-D
     */
    OTHER_CELL_D("otherCell-D"),

    /**
     * 其他小区-E
     */
    OTHER_CELL_E("otherCell-E"),

    /**
     * 其他小区-F
     */
    OTHER_CELL_F("otherCell-F"),

    /**
     * 存疑主室分小区A
     */
    LOW_CONFIDENCE_MAIN_INDOOR_CELL_A("lowConfidenceMainIndoorCell-A"),

    /**
     * 存疑辅室分小区A
     */
    LOW_CONFIDENCE_AUXILIARY_INDOOR_CELL_A("lowConfidenceAuxiliaryIndoorCell-A"),

    /**
     * 存疑备室分小区A(保留室分小区)
     */
    LOW_CONFIDENCE_ALTERNATIVE_AUXILIARY_INDOOR_CELL_A("lowConfidenceAlternativeAuxiliaryIndoorCell-A"),

    /**
     * 存疑主室分小区B
     */
    LOW_CONFIDENCE_MAIN_INDOOR_CELL_B("lowConfidenceMainIndoorCell-B"),

    /**
     * 存疑辅室分小区B
     */
    LOW_CONFIDENCE_AUXILIARY_INDOOR_CELL_B("lowConfidenceAuxiliaryIndoorCell-B"),

    /**
     * 存疑备室分小区B(保留室分小区)
     */
    LOW_CONFIDENCE_ALTERNATIVE_AUXILIARY_INDOOR_CELL_B("lowConfidenceAlternativeAuxiliaryIndoorCell-B"),

    DEFAULT("default");

    private String cellRole;

    CellRoleEnum(String cellRole) {
        this.cellRole = cellRole;
    }

    public String getCellRole() {
        return cellRole;
    }

    public static CellRoleEnum getCellRoleEnum(String cellRole) {
        for (CellRoleEnum cellRoleEnum : CellRoleEnum.values()) {
            if (cellRoleEnum.getCellRole().equals(cellRole)) {
                return cellRoleEnum;
            }
        }
        return DEFAULT;
    }
}
