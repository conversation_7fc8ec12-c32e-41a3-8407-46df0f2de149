package cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.data;

import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.cell.CellConfig;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.feature.BuildingFeature;
import scala.Serializable;

import java.util.StringJoiner;

public class CellAndBuilding implements Serializable {
    private CellConfig cell;
    private BuildingFeature buildingFeature;
    private Long mrCnt;
    private Long cellTotalMrCnt;

    public CellAndBuilding() {
    }

    public CellAndBuilding(CellConfig cell, BuildingFeature buildingFeature) {
        this.cell = cell;
        this.buildingFeature = buildingFeature;
        this.mrCnt = 1L;
    }

    public CellAndBuilding(CellConfig cell, BuildingFeature buildingFeature, Long mrCnt) {
        this.cell = cell;
        this.buildingFeature = buildingFeature;
        this.mrCnt = mrCnt;
    }

    public boolean valid() {
        return cell.valid() && buildingFeature.valid();
    }

    public void merge(CellAndBuilding cellAndBuilding) {
        this.mrCnt += cellAndBuilding.getMrCnt();
    }

    public void setCellTotalMrCnt(Long cellTotalMrCnt) {
        this.cellTotalMrCnt = cellTotalMrCnt;
    }

    public void setBuildingFeature(BuildingFeature buildingFeature) {
        this.buildingFeature = buildingFeature;
    }

    public Long getCellTotalMrCnt() {
        return cellTotalMrCnt;
    }

    public Long getMrCnt() {
        return mrCnt;
    }

    public Long getEci() {
        return cell.getEci();
    }

    public CellConfig getCell() {
        return cell;
    }

    public BuildingFeature getBuildingFeature() {
        return buildingFeature;
    }

    @Override
    public String toString() {
        return new StringJoiner("\t")
                .add(String.valueOf(buildingFeature.getCityId()))
                .add(String.valueOf(cell.getEci()))
                .add(String.valueOf(buildingFeature.getId()))
                .add(String.valueOf(mrCnt))
                .toString();
    }
}