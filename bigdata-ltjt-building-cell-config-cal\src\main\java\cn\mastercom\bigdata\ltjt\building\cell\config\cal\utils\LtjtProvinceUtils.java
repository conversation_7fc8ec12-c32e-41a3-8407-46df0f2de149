package cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils;

import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.ProvinceEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import scala.Serializable;

import java.util.*;

public class LtjtProvinceUtils implements Serializable {
    private static final Logger logger = LoggerFactory.getLogger(LtjtProvinceUtils.class);
    private static final Map<Integer, ProvinceEnum> LTJT_ID_MAP = new HashMap<>();

    private LtjtProvinceUtils() {
        throw new IllegalStateException("Utility class");
    }

    static {
        LTJT_ID_MAP.put(101, ProvinceEnum.BEIJING);
        LTJT_ID_MAP.put(102, ProvinceEnum.SHANGHAI);
        LTJT_ID_MAP.put(103, ProvinceEnum.GUANGDONG);
        LTJT_ID_MAP.put(104, ProvinceEnum.CHONGQING);
        LTJT_ID_MAP.put(105, ProvinceEnum.HUNAN);
        LTJT_ID_MAP.put(106, ProvinceEnum.QINGHAI);
        LTJT_ID_MAP.put(107, ProvinceEnum.ANHUI);
        LTJT_ID_MAP.put(108, ProvinceEnum.FUJIAN);
        LTJT_ID_MAP.put(109, ProvinceEnum.GANSU);
        LTJT_ID_MAP.put(110, ProvinceEnum.GUANGXI);
        LTJT_ID_MAP.put(111, ProvinceEnum.HAINAN);
        LTJT_ID_MAP.put(112, ProvinceEnum.JIANGXI);
        LTJT_ID_MAP.put(113, ProvinceEnum.TIANJIN);
        LTJT_ID_MAP.put(114, ProvinceEnum.JILIN);
        LTJT_ID_MAP.put(115, ProvinceEnum.HEILONGJIANG);
        LTJT_ID_MAP.put(116, ProvinceEnum.LIAONING);
        LTJT_ID_MAP.put(117, ProvinceEnum.HUBEI);
        LTJT_ID_MAP.put(118, ProvinceEnum.XINJIANG);
        LTJT_ID_MAP.put(119, ProvinceEnum.NINGXIA);
        LTJT_ID_MAP.put(120, ProvinceEnum.YUNNAN);
        LTJT_ID_MAP.put(121, ProvinceEnum.ZHEJIANG);
        LTJT_ID_MAP.put(122, ProvinceEnum.SHANDONG);
        LTJT_ID_MAP.put(123, ProvinceEnum.XIZANG);
        LTJT_ID_MAP.put(124, ProvinceEnum.NEIMENG);
        LTJT_ID_MAP.put(125, ProvinceEnum.HENAN);
        LTJT_ID_MAP.put(126, ProvinceEnum.SICHUAN);
        LTJT_ID_MAP.put(127, ProvinceEnum.HEBEI);
        LTJT_ID_MAP.put(128, ProvinceEnum.SHANXI);
        LTJT_ID_MAP.put(129, ProvinceEnum.SHAANXI);
        LTJT_ID_MAP.put(130, ProvinceEnum.JIANGSU);
        LTJT_ID_MAP.put(131, ProvinceEnum.GUIZHOU);
    }

    public static ProvinceEnum getProvinceByLtjtId(Integer id) {
        if (id == null) {
            logger.warn("=== Input ID is null, returning OTHER ===");
            return ProvinceEnum.OTHER;
        }

        ProvinceEnum provinceEnum = LTJT_ID_MAP.get(id);
        if (provinceEnum == null) {
            logger.warn("=== Unknown ID: {}, returning OTHER ===", id);
            return ProvinceEnum.OTHER;
        }

        return provinceEnum;
    }

    private static final Map<String, ProvinceEnum> ABBREVIATIONS_MAP = new HashMap<>();

    static {
        ABBREVIATIONS_MAP.put("BJ", ProvinceEnum.BEIJING);
        ABBREVIATIONS_MAP.put("SH", ProvinceEnum.SHANGHAI);
        ABBREVIATIONS_MAP.put("GD", ProvinceEnum.GUANGDONG);
        ABBREVIATIONS_MAP.put("CQ", ProvinceEnum.CHONGQING);
        ABBREVIATIONS_MAP.put("HUN", ProvinceEnum.HUNAN);
        ABBREVIATIONS_MAP.put("QH", ProvinceEnum.QINGHAI);
        ABBREVIATIONS_MAP.put("AH", ProvinceEnum.ANHUI);
        ABBREVIATIONS_MAP.put("FJ", ProvinceEnum.FUJIAN);
        ABBREVIATIONS_MAP.put("GS", ProvinceEnum.GANSU);
        ABBREVIATIONS_MAP.put("GX", ProvinceEnum.GUANGXI);
        ABBREVIATIONS_MAP.put("HN", ProvinceEnum.HAINAN);
        ABBREVIATIONS_MAP.put("JX", ProvinceEnum.JIANGXI);
        ABBREVIATIONS_MAP.put("TJ", ProvinceEnum.TIANJIN);
        ABBREVIATIONS_MAP.put("JL", ProvinceEnum.JILIN);
        ABBREVIATIONS_MAP.put("HLJ", ProvinceEnum.HEILONGJIANG);
        ABBREVIATIONS_MAP.put("LN", ProvinceEnum.LIAONING);
        ABBREVIATIONS_MAP.put("HUB", ProvinceEnum.HUBEI);
        ABBREVIATIONS_MAP.put("XJ", ProvinceEnum.XINJIANG);
        ABBREVIATIONS_MAP.put("NX", ProvinceEnum.NINGXIA);
        ABBREVIATIONS_MAP.put("YN", ProvinceEnum.YUNNAN);
        ABBREVIATIONS_MAP.put("ZJ", ProvinceEnum.ZHEJIANG);
        ABBREVIATIONS_MAP.put("SD", ProvinceEnum.SHANDONG);
        ABBREVIATIONS_MAP.put("XZ", ProvinceEnum.XIZANG);
        ABBREVIATIONS_MAP.put("NM", ProvinceEnum.NEIMENG);
        ABBREVIATIONS_MAP.put("HEN", ProvinceEnum.HENAN);
        ABBREVIATIONS_MAP.put("SC", ProvinceEnum.SICHUAN);
        ABBREVIATIONS_MAP.put("HEB", ProvinceEnum.HEBEI);
        ABBREVIATIONS_MAP.put("SX", ProvinceEnum.SHAANXI);
        ABBREVIATIONS_MAP.put("SXI", ProvinceEnum.SHANXI);
        ABBREVIATIONS_MAP.put("JS", ProvinceEnum.JIANGSU);
        ABBREVIATIONS_MAP.put("GZ", ProvinceEnum.GUIZHOU);
    }

    public static ProvinceEnum getProvinceByAbbreviationsName(String abbreviationsName) {
        if (abbreviationsName == null || abbreviationsName.trim().isEmpty()) {
            return ProvinceEnum.OTHER;
        }

        String upperCaseAbbreviation = abbreviationsName.trim().toUpperCase();
        ProvinceEnum provinceEnum = ABBREVIATIONS_MAP.get(upperCaseAbbreviation);
        if (provinceEnum == null) {
            logger.warn("=== Unknown ABBREVIATIONS: {}, returning OTHER ===", upperCaseAbbreviation);
            return ProvinceEnum.OTHER;
        }
        return provinceEnum;
    }

    private static final Map<String, ProvinceEnum> PROVINCE_MAP = new HashMap<>();

    static {
        // 初始化省份映射表
        PROVINCE_MAP.put("beijing", ProvinceEnum.BEIJING);
        PROVINCE_MAP.put("shanghai", ProvinceEnum.SHANGHAI);
        PROVINCE_MAP.put("guangdong", ProvinceEnum.GUANGDONG);
        PROVINCE_MAP.put("chongqing", ProvinceEnum.CHONGQING);
        PROVINCE_MAP.put("hunan", ProvinceEnum.HUNAN);
        PROVINCE_MAP.put("qinghai", ProvinceEnum.QINGHAI);
        PROVINCE_MAP.put("anhui", ProvinceEnum.ANHUI);
        PROVINCE_MAP.put("fujian", ProvinceEnum.FUJIAN);
        PROVINCE_MAP.put("gansu", ProvinceEnum.GANSU);
        PROVINCE_MAP.put("guangxi", ProvinceEnum.GUANGXI);
        PROVINCE_MAP.put("hainan", ProvinceEnum.HAINAN);
        PROVINCE_MAP.put("jiangxi", ProvinceEnum.JIANGXI);
        PROVINCE_MAP.put("tianjin", ProvinceEnum.TIANJIN);
        PROVINCE_MAP.put("jilin", ProvinceEnum.JILIN);
        PROVINCE_MAP.put("heilongjiang", ProvinceEnum.HEILONGJIANG);
        PROVINCE_MAP.put("liaoning", ProvinceEnum.LIAONING);
        PROVINCE_MAP.put("hubei", ProvinceEnum.HUBEI);
        PROVINCE_MAP.put("xinjiang", ProvinceEnum.XINJIANG);
        PROVINCE_MAP.put("ningxia", ProvinceEnum.NINGXIA);
        PROVINCE_MAP.put("yunnan", ProvinceEnum.YUNNAN);
        PROVINCE_MAP.put("zhejiang", ProvinceEnum.ZHEJIANG);
        PROVINCE_MAP.put("shandong", ProvinceEnum.SHANDONG);
        PROVINCE_MAP.put("xizang", ProvinceEnum.XIZANG);
        PROVINCE_MAP.put("neimeng", ProvinceEnum.NEIMENG);
        PROVINCE_MAP.put("henan", ProvinceEnum.HENAN);
        PROVINCE_MAP.put("sichuan", ProvinceEnum.SICHUAN);
        PROVINCE_MAP.put("hebei", ProvinceEnum.HEBEI);
        PROVINCE_MAP.put("shaanxi", ProvinceEnum.SHAANXI);
        PROVINCE_MAP.put("shanxi", ProvinceEnum.SHANXI);
        PROVINCE_MAP.put("jiangsu", ProvinceEnum.JIANGSU);
        PROVINCE_MAP.put("guizhou", ProvinceEnum.GUIZHOU);
    }

    public static ProvinceEnum getProvinceByQuanPinName(String quanPinName) {
        if (quanPinName == null || quanPinName.trim().isEmpty()) {
            return ProvinceEnum.OTHER;
        }

        String lowerCaseName = quanPinName.toLowerCase();
        ProvinceEnum provinceEnum = ABBREVIATIONS_MAP.get(lowerCaseName);
        if (provinceEnum == null) {
            logger.warn("=== Unknown QuanPin Name: {}, returning OTHER ===", lowerCaseName);
            return ProvinceEnum.OTHER;
        }
        return PROVINCE_MAP.getOrDefault(lowerCaseName, ProvinceEnum.OTHER);
    }

    public static List<ProvinceEnum> getAllProvinceList() {
        return new ArrayList<>(Arrays.asList(ProvinceEnum.values()));
    }
}