package cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.key;

import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.cell.CellConfig;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.data.CellAndBuilding;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.data.MdtSample;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.res.BuildingCellConfig;
import scala.Serializable;

import java.util.Objects;

public class CellKey implements Serializable {

    private final Long eci;

    public CellKey(Long eci) {
        this.eci = eci;
    }

    public CellKey(CellConfig cellConfig) {
        this.eci = cellConfig.getEci();
    }

    public CellKey(BuildingCellConfig buildingCellConfig) {
        this.eci = buildingCellConfig.getEci();
    }

    public CellKey(CellAndBuilding cellAndBuilding) {
        this.eci = cellAndBuilding.getCell().getEci();
    }

    public CellKey(MdtSample mdtSample) {
        this.eci = mdtSample.getEci();
    }

    public boolean valid() {
        return this.eci > 0;
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof CellKey)) {
            return false;
        }

        CellKey cellKey = (CellKey) obj;
        return eci.equals(cellKey.getEci());
    }

    @Override
    public int hashCode() {
        return Objects.hash(eci);
    }

    public Long getEci() {
        return eci;
    }

    @Override
    public String toString() {
        return String.valueOf(eci);
    }
}
