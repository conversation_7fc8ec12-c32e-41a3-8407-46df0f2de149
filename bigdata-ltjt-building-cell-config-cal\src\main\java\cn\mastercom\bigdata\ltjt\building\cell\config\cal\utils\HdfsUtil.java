package cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils;

import cn.mastercom.mtcommon.spark.HdfsUtils;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class HdfsUtil {

    private HdfsUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static boolean prepareForOutput(String path) {
        try {
            if (HdfsUtils.pathExist(path)) {
                HdfsUtil.renameDir(path);
            }
        } catch (Exception ignored) {
            return false;
        }
        return true;
    }

    public static void renameDir(String oldPath) throws IOException {
        Date now = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyMMddHHmmss");
        String nowStr = dateFormat.format(now);
        String newPath = oldPath + "_" + nowStr;
        HdfsUtils.rename(oldPath, newPath);
    }
}