package cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils;

import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.ProvinceEnum;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.feature.BuildingFeature;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.key.BuildingKey;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.layer.BuildingLayer;
import org.apache.spark.api.java.JavaPairRDD;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.api.java.function.Function;
import org.apache.spark.api.java.function.PairFunction;
import org.apache.spark.broadcast.Broadcast;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import scala.Tuple2;

import java.io.Serializable;

public class LayerUtils implements Serializable {
    private static final Logger logger = LoggerFactory.getLogger(LayerUtils.class);

    private LayerUtils() {
        throw new IllegalStateException("Utility class");
    }

    public static Broadcast<BuildingLayer> getBuildingLayerBroadcast(SparkSession sparkSession, JavaSparkContext jsc, ProvinceEnum workProvince) {
        String getBuildingLayerSql = LtjtPathUtils.getBuildingLayerSql(workProvince);
        BuildingLayer buildingLayer = new BuildingLayer();

        buildingLayer.initFromHive(sparkSession, getBuildingLayerSql);
        if (!buildingLayer.valid()) {
            logger.warn("=== buildingLayer is not valid ===");
            buildingLayer.setSize(0);
        }
        return jsc.broadcast(buildingLayer);
    }

    public static JavaPairRDD<BuildingKey, BuildingFeature> getBuildingFeaturePairRDD(SparkSession sparkSession, ProvinceEnum workProvince) {
        String getBuildingLayerSql = LtjtPathUtils.getBuildingLayerSql(workProvince);
        JavaRDD<Row> rawDataRDD = SourceUtils.read(sparkSession, getBuildingLayerSql, "buildingFeature");
        if (null == rawDataRDD) {
            return null;
        }

        return rawDataRDD
                .map((Function<Row, BuildingFeature>) BuildingFeature::new)
                .filter((Function<BuildingFeature, Boolean>) BuildingFeature::valid)
                .mapToPair((PairFunction<BuildingFeature, BuildingKey, BuildingFeature>) buildingFeature ->
                        new Tuple2<>(new BuildingKey(buildingFeature), buildingFeature));
    }
}