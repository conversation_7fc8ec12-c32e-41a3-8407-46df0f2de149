package cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.key;

import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.data.CellAndBuilding;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.feature.BuildingFeature;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.res.BuildingCellConfig;
import scala.Serializable;

import java.util.Objects;

public class BuildingKey implements Serializable {

    private final Integer cityId;
    private final Integer buildingId;

    public BuildingKey() {
        this.cityId = -1;
        this.buildingId = -1;
    }

    public BuildingKey(Integer cityId, Integer buildingId) {
        this.cityId = cityId;
        this.buildingId = buildingId;
    }

    public BuildingKey(BuildingCellConfig buildingCellConfig) {
        this.cityId = buildingCellConfig.getCityId();
        this.buildingId = buildingCellConfig.getBuildingId();
    }

    public BuildingKey(BuildingFeature buildingFeature) {
        this.cityId = buildingFeature.getCityId();
        this.buildingId = buildingFeature.getId();
    }

    public BuildingKey(CellAndBuilding cellAndBuilding) {
        this.cityId = cellAndBuilding.getBuildingFeature().getCityId();
        this.buildingId = cellAndBuilding.getBuildingFeature().getId();
    }

    public boolean valid() {
        return this.cityId != -1 && this.buildingId != -1;
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof BuildingKey)) {
            return false;
        }

        BuildingKey buildingKey = (BuildingKey) obj;
        return cityId.equals(buildingKey.getCityId()) &&
                buildingId.equals(buildingKey.getBuildingId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(cityId, buildingId);
    }

    public Integer getCityId() {
        return cityId;
    }

    public Integer getBuildingId() {
        return buildingId;
    }

    @Override
    public String toString() {
        return cityId + "-" + buildingId;
    }
}
