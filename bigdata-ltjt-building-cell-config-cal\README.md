# 联通集团立体覆盖楼宇小区配置关系计算系统

## 项目概述

本项目是联通集团立体覆盖系统的重要组成部分，专注于通过MDT（Minimization of Drive Tests）采样点数据以及一系列配置规则，计算楼宇与室分小区的覆盖关系。系统能够识别主室分小区、辅室分小区、备室分小区等不同角色的小区，为网络优化和精准覆盖提供数据支撑。

## 技术架构

### 技术栈
- **编程语言**: Java 8
- **大数据框架**: Apache Spark 2.1.0
- **构建工具**: Maven 3.x
- **数据存储**: HDFS、Hive
- **地理空间**: S2几何库
- **日志框架**: SLF4J + Logback

### 核心依赖
- **Spark**: spark-core_2.11, spark-sql_2.11, spark-hive_2.11
- **Hadoop**: hadoop-client 2.7.0
- **GIS支持**: s2-geometry-library-java 1.0.0
- **数据库**: mysql-connector-java 5.1.47
- **内部组件**: bigdata-common, bigdata-hadoop-tools, mtcommon系列

## 功能特性

### 1. 双模式计算
- **日粒度计算**: 按天处理MDT数据，生成每日楼宇小区配置关系
- **月粒度计算**: 按月汇聚日粒度结果，生成月度楼宇小区配置关系

### 2. 小区角色识别
系统能够识别以下小区角色：

| 角色类型 | 描述 |
|---------|------|
| **主室分小区** | 采样点最多的小区，为主要覆盖该楼宇的室分小区 |
| **辅室分小区** | 采样点次多的小区，为辅助覆盖该楼宇的室分小区 |
| **备室分小区** | 保留的次覆盖楼宇，作为备选室分小区 |
| **其他小区A-F** | 不符合室分条件的各类小区 |
| **存疑小区A/B** | 置信度较低的各类小区 |

### 3. 智能过滤规则
- **采样点数量过滤**: 总采样点小于300的小区直接过滤
- **比例阈值过滤**: 楼宇内采样点占小区总采样点比例小于90%的小区过滤
- **置信度评估**: 基于历史数据和比例关系评估小区角色置信度

### 4. 历史数据融合
- 利用前两月的历史数据识别稳定的备室分小区
- 支持历史数据的平滑过渡和角色继承

## 数据模型

### 核心数据结构

#### BuildingCellConfig
楼宇小区配置关系的核心数据结构，包含以下字段：
- `cityId`: 城市ID
- `buildingId`: 楼宇ID
- `buildingName`: 楼宇名称
- `buildingCenterLongitude/Latitude`: 楼宇中心经纬度
- `eci`: 小区ECI标识
- `cellLongitude/Latitude`: 小区经纬度
- `mrCnt`: 该楼宇内该小区的采样点数
- `cellTotalMrCnt`: 该小区总采样点数
- `cellRole`: 小区角色枚举
- `isBuildingIndoorCoverage`: 是否为楼宇室分覆盖

#### CellRoleEnum
小区角色枚举，定义了16种不同的小区角色类型。

## 运行方式

### 参数说明
程序需要3个命令行参数：
1. **作业类型**: `DAY` 或 `MONTH`
2. **计算日期**: 
   - 日作业: 格式为 `yyMMdd` (如: 240715)
   - 月作业: 格式为 `yyMM` (如: 2407)
3. **省份ID**: 三位数字的省份标识 (如: 110)

### 运行示例

#### 日粒度计算
```bash
spark-submit \
  --class cn.mastercom.bigdata.ltjt.building.cell.config.cal.main.SparkAppMain \
  --master yarn \
  --deploy-mode cluster \
  bigdata-ltjt-building-cell-config-cal-1.6.jar \
  DAY 240715 110
```

#### 月粒度计算
```bash
spark-submit \
  --class cn.mastercom.bigdata.ltjt.building.cell.config.cal.main.SparkAppMain \
  --master yarn \
  --deploy-mode cluster \
  bigdata-ltjt-building-cell-config-cal-1.6.jar \
  MONTH 2407 110
```

## 数据流程

### 1. 数据输入
- **MDT采样点数据**: 来自HDFS的每日MDT数据
- **楼宇图层数据**: 来自Hive的楼宇基础信息
- **室分小区配置**: 来自Hive的室分小区列表
- **历史配置数据**: 前两月的楼宇小区配置结果

### 2. 数据处理流程
```
输入数据 → 数据预处理 → 小区角色计算 → 结果过滤 → 结果输出
```

### 3. 数据输出
- **日粒度结果**: 每日楼宇小区配置关系
- **月粒度结果**: 月度汇聚的楼宇小区配置关系
- **输出路径**: `hdfs://hdfsunity/data/wndoidAdmin_yuhui/hive/warehouse/ads_wndoidadmin_yuhui.db/config/{省份}/building-cell-config/{年月}/tb_building_cell_config`

## 配置管理

### 路径配置
所有路径配置集中在 `LtjtPathUtils` 类中：
- **MDT数据路径**: 按省份和日期组织的HDFS路径
- **楼宇图层SQL**: 从Hive获取楼宇信息的SQL模板
- **室分小区SQL**: 从Hive获取室分小区列表的SQL模板
- **输出路径**: 结果数据的HDFS存储路径

### 业务参数
- **最大扩展距离**: 80米（楼宇与小区的最大关联距离）
- **采样点阈值**: 300（最小有效采样点数）
- **比例阈值**: 90%（楼宇内采样点占比阈值）

## 部署与构建

### 构建命令
```bash
mvn clean package -DskipTests
```

### 部署文件
构建完成后，生成的可执行JAR文件位于：
```
target/bigdata-ltjt-building-cell-config-cal-1.6.jar
```

### 环境要求
- **Java**: 1.8+
- **Spark**: 2.1.0+
- **Hadoop**: 2.7.0+
- **Hive**: 支持Spark SQL访问

## 监控与日志

### 日志配置
- **日志级别**: INFO级别记录主要处理步骤
- **日志文件**: 按日期分割的info和error日志
- **日志路径**: `log/` 目录下

### 监控指标
- **处理记录数**: 每日/月处理的MDT记录数
- **输出记录数**: 生成的楼宇小区配置关系数
- **处理时长**: 作业执行时间统计
- **错误率**: 异常数据处理比例

## 扩展性设计

### 省份扩展
支持通过配置快速扩展到新的省份，只需：
1. 在 `ProvinceEnum` 中添加新省份枚举
2. 配置对应的省份ID和名称映射
3. 无需修改核心计算逻辑

### 规则扩展
小区角色识别规则可通过修改 `BuildingCellConfigUtils` 类进行扩展，支持：
- 新增角色类型
- 调整阈值参数
- 增加新的过滤条件

## 注意事项

1. **数据一致性**: 确保输入的MDT数据、楼宇数据、室分小区数据的时间一致性
2. **资源管理**: 月粒度计算涉及大量数据汇聚，需要充足的Spark资源
3. **历史依赖**: 月粒度计算依赖前两月的结果，确保历史数据完整性
4. **异常处理**: 程序包含完善的异常处理机制，但建议定期监控日志

## 支持与维护

### 问题排查
- 检查日志文件中的ERROR级别信息
- 验证输入数据的完整性和格式正确性
- 确认HDFS路径和Hive表的访问权限

### 性能优化
- 根据数据量调整Spark并行度
- 合理设置executor内存和核数
- 考虑使用数据缓存优化重复计算

### 版本历史
- **v1.6**: 当前版本，支持完整的楼宇小区配置关系计算
