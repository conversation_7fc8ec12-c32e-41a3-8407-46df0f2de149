package cn.mastercom.bigdata.ltjt.building.cell.config.cal.main;

import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.data.CellAndBuilding;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.key.BuildingKey;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.key.CellBuildingKey;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.key.CellKey;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.res.BuildingCellConfig;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils.BuildingCellConfigUtils;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils.CellUtils;
import org.apache.spark.api.java.JavaPairRDD;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.api.java.Optional;
import org.apache.spark.api.java.function.FlatMapFunction;
import org.apache.spark.api.java.function.Function;
import org.apache.spark.api.java.function.PairFunction;
import org.apache.spark.broadcast.Broadcast;
import org.apache.spark.storage.StorageLevel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import scala.Serializable;
import scala.Tuple2;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class MonthJob implements Serializable {
    private static final Logger logger = LoggerFactory.getLogger(MonthJob.class);

    private final String taskMonth;
    private final JavaRDD<CellAndBuilding> cellAndBuildingRDD;
    private final Set<BuildingKey> indoorCoverageBuildingIdSet;
    private final Set<CellBuildingKey> historyAlternativeAuxiliaryIndoorCellSet;

    private JavaRDD<BuildingCellConfig> resBuildingCellConfigRDD;

    public MonthJob(String taskMonth, JavaRDD<CellAndBuilding> cellAndBuildingRDD,
                    Broadcast<List<BuildingKey>> indoorCoverageBuildingIdListBroadcast,
                    Broadcast<List<CellBuildingKey>> historyAlternativeAuxiliaryIndoorCellListBroadcast) {
        this.taskMonth = taskMonth;
        this.cellAndBuildingRDD = cellAndBuildingRDD;

        List<BuildingKey> indoorCoverageBuildingIdList = indoorCoverageBuildingIdListBroadcast.getValue();
        if (indoorCoverageBuildingIdList != null) {
            this.indoorCoverageBuildingIdSet = Collections.unmodifiableSet(new HashSet<>(indoorCoverageBuildingIdList));
        } else {
            this.indoorCoverageBuildingIdSet = Collections.emptySet();
        }

        List<CellBuildingKey> historyAlternativeAuxiliaryIndoorCellList =
                historyAlternativeAuxiliaryIndoorCellListBroadcast.getValue();
        if (historyAlternativeAuxiliaryIndoorCellList != null) {
            this.historyAlternativeAuxiliaryIndoorCellSet =
                    Collections.unmodifiableSet(new HashSet<>(historyAlternativeAuxiliaryIndoorCellList));
        } else {
            this.historyAlternativeAuxiliaryIndoorCellSet = Collections.emptySet();
        }
    }

    public void work() {
        logger.info("=== Begin deal {} ===", taskMonth);

        JavaPairRDD<CellKey, Boolean> cellKeysInTop90PercentilePairRDD = CellUtils
                .getCellKeysInTop90PercentileBySampling(cellAndBuildingRDD)
                .mapToPair((PairFunction<CellKey, CellKey, Boolean>) cellKey -> new Tuple2<>(cellKey, true));

        this.resBuildingCellConfigRDD = cellAndBuildingRDD
                .filter((Function<CellAndBuilding, Boolean>) CellAndBuilding::valid)
                .mapToPair((PairFunction<CellAndBuilding, CellKey, CellAndBuilding>) cellAndBuilding ->
                        new Tuple2<>(new CellKey(cellAndBuilding), cellAndBuilding))
                .groupByKey()
                .leftOuterJoin(cellKeysInTop90PercentilePairRDD)
                .values()
                .flatMap((FlatMapFunction<Tuple2<Iterable<CellAndBuilding>, Optional<Boolean>>, BuildingCellConfig>) tuple2 -> {
                    boolean isInTop90Percentile = tuple2._2().isPresent();
                    return BuildingCellConfigUtils
                            .dealOneCell(tuple2._1(), isInTop90Percentile, indoorCoverageBuildingIdSet, historyAlternativeAuxiliaryIndoorCellSet)
                            .iterator();
                })
                .map((Function<BuildingCellConfig, BuildingCellConfig>) buildingCellConfig -> {
                    boolean isBuildingIndoorCoverage = indoorCoverageBuildingIdSet.contains(new BuildingKey(buildingCellConfig));
                    buildingCellConfig.setBuildingIndoorCoverage(isBuildingIndoorCoverage);
                    return buildingCellConfig;
                });

        resBuildingCellConfigRDD.persist(StorageLevel.MEMORY_AND_DISK());
        long resCnt = resBuildingCellConfigRDD.count();
        logger.info("=== End deal {}, total get {} res ===", taskMonth, resCnt);
    }

    public JavaRDD<BuildingCellConfig> getResBuildingCellConfigRDD() {
        return resBuildingCellConfigRDD;
    }
}
