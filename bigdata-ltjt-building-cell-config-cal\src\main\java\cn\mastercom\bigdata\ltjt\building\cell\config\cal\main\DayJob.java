package cn.mastercom.bigdata.ltjt.building.cell.config.cal.main;

import cn.mastercom.bigdata.ltjt.building.cell.config.cal.conf.JobInfo;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.ProvinceEnum;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.cell.CellConfig;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.data.CellAndBuilding;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.data.MdtSample;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.feature.BuildingFeature;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.key.CellBuildingKey;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.key.CellKey;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.layer.BuildingLayer;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils.*;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.api.java.function.Function;
import org.apache.spark.api.java.function.Function2;
import org.apache.spark.api.java.function.PairFunction;
import org.apache.spark.broadcast.Broadcast;
import org.apache.spark.storage.StorageLevel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import scala.Serializable;
import scala.Tuple2;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public class DayJob implements Serializable {
    private static final Logger logger = LoggerFactory.getLogger(DayJob.class);

    private final LocalDate taskDate;
    private final ProvinceEnum workProvince;
    private final Integer maxExpandDis;
    private final Broadcast<BuildingLayer> buildingLayerBroadcast;
    private final Broadcast<List<CellConfig>> indoorcellConfigListBroadcast;


    public DayJob(LocalDate taskDate, ProvinceEnum workProvince, int maxExpandDis,
                  Broadcast<BuildingLayer> buildingLayerBroadcast,
                  Broadcast<List<CellConfig>> indoorCellConfigListBroadcast) {
        this.taskDate = taskDate;
        this.workProvince = workProvince;
        this.maxExpandDis = maxExpandDis;
        this.buildingLayerBroadcast = buildingLayerBroadcast;
        this.indoorcellConfigListBroadcast = indoorCellConfigListBroadcast;
    }

    public void work(JavaSparkContext jsc) {
        String taskDateStr = DateUtils.transDateToString(taskDate, JobInfo.YYYY_MM_DD);
        logger.info("=== Begin deal {} daily-job ===", taskDateStr);

        JavaRDD<MdtSample> allMdtSampleRDD = MdtSampleUtils
                .readMdtSample(jsc, workProvince, taskDate);
        if (null == allMdtSampleRDD) {
            return;
        }
        List<CellKey> indoorCellEciList = CellUtils.getAllEci(indoorcellConfigListBroadcast.value());
        // 过滤掉非室分小区以及经纬度异常和eci异常采样点
        JavaRDD<MdtSample> indoorCellSampleRDD = allMdtSampleRDD
                .filter((Function<MdtSample, Boolean>) mdtSample ->
                        mdtSample.valid() && indoorCellEciList.contains(new CellKey(mdtSample)));
        indoorCellSampleRDD.persist(StorageLevel.MEMORY_AND_DISK());

        MdtSampleUtils.statCellTotalMrCnt(indoorCellSampleRDD, workProvince, taskDate);

        JavaRDD<CellAndBuilding> eciAndBuildingRDD = generateCellAndBuildingRes(indoorCellSampleRDD);
        String cellAndBuildingOutputPath = LtjtPathUtils
                .getOutputPath("cell-and-building", taskDate, workProvince);
        if (HdfsUtil.prepareForOutput(cellAndBuildingOutputPath)) {
            eciAndBuildingRDD.repartition(2)
                    .saveAsTextFile(cellAndBuildingOutputPath);
            logger.info("=== End dealing {}, total get {} Eci-Building-Res ===", taskDateStr, eciAndBuildingRDD.count());
        }
    }

    private JavaRDD<CellAndBuilding> generateCellAndBuildingRes(JavaRDD<MdtSample> mdtSampleRDD) {
        Map<CellKey, CellConfig> eciCellConfigMap = CellUtils.getEciCellConfigMap(indoorcellConfigListBroadcast.value());

        return mdtSampleRDD
                // 采样点关联楼宇图层，然后汇聚得到 eci-buildingId-mrCnt
                .map((Function<MdtSample, CellAndBuilding>) mdtSample -> {
                    CellConfig cellConfig = eciCellConfigMap.get(new CellKey(mdtSample));
                    double longitude = mdtSample.getLongitude();
                    double latitude = mdtSample.getLatitude();
                    List<Tuple2<BuildingFeature, Integer>> tuple2List = FeatureUtil
                            .find(true, true, longitude, latitude, maxExpandDis, buildingLayerBroadcast);
                    if (tuple2List.isEmpty()) {
                        return new CellAndBuilding(cellConfig, new BuildingFeature());
                    }
                    return new CellAndBuilding(cellConfig, tuple2List.get(0)._1());
                })
                .mapToPair((PairFunction<CellAndBuilding, CellBuildingKey, CellAndBuilding>) eciAndBuilding ->
                        new Tuple2<>(new CellBuildingKey(eciAndBuilding), eciAndBuilding))
                .reduceByKey((Function2<CellAndBuilding, CellAndBuilding, CellAndBuilding>) (v1, v2) -> {
                    v1.merge(v2);
                    return v1;
                })
                .values();
    }
}