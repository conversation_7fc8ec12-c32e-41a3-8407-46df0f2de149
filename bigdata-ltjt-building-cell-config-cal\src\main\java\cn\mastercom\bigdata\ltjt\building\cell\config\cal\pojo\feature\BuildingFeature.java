package cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.feature;

import ch.qos.logback.access.spi.IAccessEvent;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils.WorkUtils;
import cn.mastercom.mtcommon.gis.DRect;
import cn.mastercom.mtcommon.gis.GisFactory;
import cn.mastercom.mtcommon.spark.maplayer.feature.AbstractFeature;
import cn.mastercom.mtcommon.utils.HexUtils;
import org.apache.spark.sql.Row;

public class BuildingFeature extends AbstractFeature {
    private int cityId;
    private String buildingName;
    private double centerLongitude;
    private double centerLatitude;

    public BuildingFeature() {
        this.cityId = -1;
        this.id = -1;
        this.buildingName = "";
        this.centerLongitude = WorkUtils.INVALID_DOUBLE_VALUE;
        this.centerLatitude = WorkUtils.INVALID_DOUBLE_VALUE;
    }

    public BuildingFeature(Row row) {
        int index = 0;

        byte[] buffer;
        try {
            this.cityId = row.getInt(index++);
            this.id = row.getInt(index++);
            this.buildingName = row.getString(index++);
            this.centerLongitude = row.getDouble(index++);
            this.centerLatitude = row.getDouble(index++);
            String tmpStr = row.getString(index);
            if (tmpStr.contains("0x") || tmpStr.contains("0X")) {
                buffer = HexUtils.decode(tmpStr.substring(2));
            } else {
                buffer = HexUtils.decode(tmpStr);
            }
            this.shape = GisFactory.newPloygon(buffer);
        } catch (Exception e) {
            this.cityId = -1;
        }
    }

    @Override
    public boolean valid() {
        return super.valid() && this.cityId != -1;
    }

    public boolean intersects(DRect dRect) {
        return this.shape.intersects(dRect);
    }

    public String getKey() {
        return this.cityId + IAccessEvent.NA + this.id;
    }

    public double getCenterLongitude() {
        return this.centerLongitude;
    }

    public double getCenterLatitude() {
        return this.centerLatitude;
    }

    public int getId() {
        return this.id;
    }

    public String getBuildingName() {
        return this.buildingName;
    }

    public int getCityId() {
        return this.cityId;
    }

    @Override
    public boolean intersects(double longitude, double latitude) {
        return this.shape.intersects(longitude, latitude);
    }

    public boolean intersects(double tlLongitude, double tlLatitude, double brLongitude, double brLatitude) {
        return this.shape.intersects(new DRect(tlLongitude, brLatitude, brLongitude, tlLatitude));
    }
}