package cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo;

import java.io.Serializable;

public enum TaskDataTypeEnum implements Serializable {

    /**
     * 天粒度作业
     */
    DAY("day"),

    /**
     * 月粒度作业
     */
    MONTH("month"),

    DEFAULT("default");

    private String taskDataType;

    TaskDataTypeEnum(String taskDataType) {
        this.taskDataType = taskDataType;
    }

    public String getTaskDataType() {
        return taskDataType;
    }

    public static TaskDataTypeEnum getTaskDataType(String taskDateType) {
        for (TaskDataTypeEnum taskDataTypeEnum : TaskDataTypeEnum.values()) {
            if (taskDataTypeEnum.getTaskDataType().equals(taskDateType)) {
                return taskDataTypeEnum;
            }
        }
        return DEFAULT;
    }
}
