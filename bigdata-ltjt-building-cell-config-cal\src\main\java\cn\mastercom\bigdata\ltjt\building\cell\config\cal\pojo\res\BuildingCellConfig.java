package cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.res;

import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.CellRoleEnum;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.cell.CellConfig;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.data.CellAndBuilding;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.feature.BuildingFeature;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils.WorkUtils;
import cn.mastercom.mtcommon.gis.GisConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.StringJoiner;

public class BuildingCellConfig implements Serializable {
    private static final Logger logger = LoggerFactory.getLogger(BuildingCellConfig.class);
    private Integer cityId;
    private Integer buildingId;
    private String buildingName;
    private Integer buildingCenterLongitude;
    private Integer buildingCenterLatitude;
    private Long eci;
    private Integer cellLongitude;
    private Integer cellLatitude;
    private Boolean isBuildingIndoorCoverage;
    private Long mrCnt;
    private Long cellTotalMrCnt;
    private CellRoleEnum cellRole;

    public BuildingCellConfig() {
    }

    public BuildingCellConfig(CellAndBuilding cellAndBuilding, CellRoleEnum cellRole) {
        BuildingFeature buildingFeature = cellAndBuilding.getBuildingFeature();
        this.cityId = buildingFeature.getCityId();
        this.buildingId = buildingFeature.getId();
        this.buildingName = buildingFeature.getBuildingName();
        this.buildingCenterLongitude = GisConverter.d2i(buildingFeature.getCenterLongitude());
        this.buildingCenterLatitude = GisConverter.d2i(buildingFeature.getCenterLatitude());

        CellConfig cell = cellAndBuilding.getCell();
        this.eci = cell.getEci();
        this.cellLongitude = GisConverter.d2i(cell.getLongitude());
        this.cellLatitude = GisConverter.d2i(cell.getLatitude());
        this.mrCnt = cellAndBuilding.getMrCnt();
        this.cellTotalMrCnt = cellAndBuilding.getCellTotalMrCnt();
        this.cellRole = cellRole;
        this.isBuildingIndoorCoverage = false;
    }

    public BuildingCellConfig(Iterable<CellAndBuilding> cellAndBuildings, CellRoleEnum cellRole) {
        CellAndBuilding cellAndBuilding = cellAndBuildings.iterator().next();
        CellConfig cell = cellAndBuilding.getCell();

        this.cityId = cell.getCityId();
        this.buildingId = 0;
        this.buildingName = "";
        this.buildingCenterLongitude = 0;
        this.buildingCenterLatitude = 0;

        this.eci = cell.getEci();
        this.cellLongitude = GisConverter.d2i(cell.getLongitude());
        this.cellLatitude = GisConverter.d2i(cell.getLatitude());
        this.mrCnt = 0L;
        this.cellTotalMrCnt = cellAndBuilding.getCellTotalMrCnt();
        this.cellRole = cellRole;
        this.isBuildingIndoorCoverage = false;
    }

    public BuildingCellConfig(String line) {
        String[] itemList = line.split("\t", -1);
        try {
            int index = 0;
            this.cityId = WorkUtils.stringToInt(itemList[index++], 0);
            this.buildingId = WorkUtils.stringToInt(itemList[index++], 0);
            this.buildingName = itemList[index++];
            this.buildingCenterLongitude = WorkUtils.stringToInt(itemList[index++], 0);
            this.buildingCenterLatitude = WorkUtils.stringToInt(itemList[index++], 0);
            this.eci = WorkUtils.stringToLong(itemList[index++], -1L);
            this.cellLongitude = WorkUtils.stringToInt(itemList[index++], 0);
            this.cellLatitude = WorkUtils.stringToInt(itemList[index++], 0);
            this.mrCnt = WorkUtils.stringToLong(itemList[index++], -1L);
            this.cellTotalMrCnt = WorkUtils.stringToLong(itemList[index++], -1L);
            this.cellRole = CellRoleEnum.getCellRoleEnum(itemList[index]);
        } catch (Exception e) {
            logger.info("=== 错误的结果数据为： {} ===", line);
            this.buildingId = -1;
        }
    }

    public boolean isAlternativeAuxiliaryIndoorCell() {
        return this.cellRole == CellRoleEnum.ALTERNATIVE_AUXILIARY_INDOOR_CELL;
    }

    public void setCellRole(CellRoleEnum cellRole) {
        this.cellRole = cellRole;
    }

    public void setBuildingIndoorCoverage(Boolean buildingIndoorCoverage) {
        this.isBuildingIndoorCoverage = buildingIndoorCoverage;
    }

    public Integer getCityId() {
        return cityId;
    }

    public Integer getBuildingId() {
        return this.buildingId;
    }

    public Long getEci() {
        return eci;
    }

    public CellRoleEnum getCellRole() {
        return cellRole;
    }

    public String toString() {
        return new StringJoiner("\t")
                .add(String.valueOf(this.cityId))
                .add(String.valueOf(this.buildingId))
                .add(this.buildingName)
                .add(String.valueOf(this.buildingCenterLongitude))
                .add(String.valueOf(this.buildingCenterLatitude))
                .add(String.valueOf(this.eci))
                .add(String.valueOf(this.cellLongitude))
                .add(String.valueOf(this.cellLatitude))
                .add(String.valueOf(this.mrCnt))
                .add(String.valueOf(this.cellTotalMrCnt))
                .add(this.cellRole.getCellRole())
                .add(String.valueOf(this.isBuildingIndoorCoverage))
                .toString();
    }
}