package cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils;

import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.feature.BuildingFeature;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.key.BuildingKey;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.layer.BuildingLayer;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.api.java.function.Function;
import org.apache.spark.api.java.function.Function2;
import org.apache.spark.api.java.function.PairFunction;
import org.apache.spark.broadcast.Broadcast;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import scala.Tuple2;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class IndoorCellBuildingConfigUtils implements Serializable {
    private static final Logger logger = LoggerFactory.getLogger(IndoorCellBuildingConfigUtils.class);

    private IndoorCellBuildingConfigUtils() {
        throw new IllegalStateException("Utility class");
    }

    public static Broadcast<List<BuildingKey>> getIndoorCoverageBuildingIdListBroadcast(
            JavaSparkContext jsc, Broadcast<BuildingLayer> buildingLayerBroadcast, String indoorCellBuildingConfigPath) {
        logger.info("=== 开始读取室分楼宇配置并进行关联处理操作，配置数据路径为 {} ===", indoorCellBuildingConfigPath);
        JavaRDD<String> rawDataRDD = SourceUtils.read(jsc, indoorCellBuildingConfigPath, "indoor-cell-building-config");
        if (null == rawDataRDD) {
            List<BuildingKey> indoorCoverageBuildingIdList = new ArrayList<>();
            logger.info("=== 室分楼宇配置处理完成，其中未读取到能够确定存在室分覆盖的楼宇,配置路径为{} ===", indoorCellBuildingConfigPath);
            return jsc.broadcast(indoorCoverageBuildingIdList);
        }

        List<BuildingKey> indoorCoverageBuildingIdList = rawDataRDD
                .mapToPair((PairFunction<String, BuildingKey, BuildingKey>) s -> {
                    String[] itemList = s.split("\t", -1);
                    if (itemList.length < 11) {
                        return new Tuple2<>(new BuildingKey(), new BuildingKey());
                    }

                    double longitude = WorkUtils.stringToDouble(itemList[2], 0.0d);
                    double latitude = WorkUtils.stringToDouble(itemList[3], 0.0d);
                    boolean isIndoorCoverageBuilding = itemList[11].trim().equals("室分已覆盖");
                    if (!isIndoorCoverageBuilding || !WorkUtils.isPositionValid(longitude, latitude)) {
                        return new Tuple2<>(new BuildingKey(), new BuildingKey());
                    }

                    List<Tuple2<BuildingFeature, Integer>> tuple2List = FeatureUtil
                            .find(true, true, longitude, latitude, 30, buildingLayerBroadcast);
                    BuildingKey key = tuple2List.isEmpty() ? new BuildingKey() : new BuildingKey(tuple2List.get(0)._1());
                    return new Tuple2<>(key, key);
                })
                .reduceByKey((Function2<BuildingKey, BuildingKey, BuildingKey>) (v1, v2) -> v1)
                .values()
                .filter((Function<BuildingKey, Boolean>) BuildingKey::valid)
                .collect();
        logger.info("=== 室分楼宇配置处理完成，其中得到能够确定存在室分覆盖的楼宇个数为{} ===", indoorCoverageBuildingIdList.size());
        return jsc.broadcast(indoorCoverageBuildingIdList);
    }
}