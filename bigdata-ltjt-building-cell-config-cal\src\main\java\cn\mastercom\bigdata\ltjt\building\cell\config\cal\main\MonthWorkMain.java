package cn.mastercom.bigdata.ltjt.building.cell.config.cal.main;

import cn.mastercom.bigdata.ltjt.building.cell.config.cal.conf.JobInfo;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.cell.CellConfig;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.data.CellAndBuilding;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.key.BuildingKey;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.key.CellBuildingKey;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.key.CellKey;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.layer.BuildingLayer;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.res.BuildingCellConfig;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils.*;
import org.apache.spark.api.java.JavaPairRDD;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.api.java.function.Function;
import org.apache.spark.api.java.function.PairFunction;
import org.apache.spark.broadcast.Broadcast;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.storage.StorageLevel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import scala.Tuple2;

import java.util.List;

public class MonthWorkMain {
    private static final Logger logger = LoggerFactory.getLogger(MonthWorkMain.class);

    private final JobInfo jobInfo;

    private Broadcast<List<CellConfig>> indoorCellListBroadcast;
    private Broadcast<List<BuildingKey>> indoorCoverageBuildingIdListBroadcast;
    private Broadcast<List<CellBuildingKey>> historyAlternativeAuxiliaryIndoorCellListBroadcast;

    private JavaRDD<CellAndBuilding> cellAndBuildingRDD = null;

    public MonthWorkMain(JobInfo jobInfo) {
        this.jobInfo = jobInfo;
    }

    public void work(SparkSession sparkSession, JavaSparkContext jsc) {
        prepare(sparkSession, jsc);

        if (!readAndMergeDailyData(sparkSession, jsc)) {
            logger.error("=== 汇聚天粒度结果错误,请检查!! ===");
            return;
        }

        MonthJob monthJob = new MonthJob(jobInfo.getTaskMonth(), cellAndBuildingRDD,
                indoorCoverageBuildingIdListBroadcast, historyAlternativeAuxiliaryIndoorCellListBroadcast);
        monthJob.work();

        JavaRDD<BuildingCellConfig> resBuildingCellConfigRDD = monthJob.getResBuildingCellConfigRDD();
        String outputPath = LtjtPathUtils.getOutputPath("tb_building_cell_config", jobInfo.getTaskMonth(),
                jobInfo.getWorkProvince());
        if (HdfsUtil.prepareForOutput(outputPath)) {
            resBuildingCellConfigRDD.repartition(1).saveAsTextFile(outputPath);
        }
    }

    private void prepare(SparkSession sparkSession, JavaSparkContext jsc) {
        indoorCellListBroadcast = CellUtils
                .getIndoorCellConfigBroadcast(sparkSession, jsc, jobInfo.getWorkProvince());
        Broadcast<BuildingLayer> buildingLayerBroadcast = LayerUtils
                .getBuildingLayerBroadcast(sparkSession, jsc, jobInfo.getWorkProvince());
        indoorCoverageBuildingIdListBroadcast = IndoorCellBuildingConfigUtils
                .getIndoorCoverageBuildingIdListBroadcast(jsc, buildingLayerBroadcast, jobInfo.getIndoorCellBuildingConfigPath());

        historyAlternativeAuxiliaryIndoorCellListBroadcast = BuildingCellConfigUtils
                .getHistoryAlternativeAuxiliaryResBroadcast(jsc, jobInfo.getWorkProvince(), jobInfo.getTaskMonth());
    }

    private boolean readAndMergeDailyData(SparkSession sparkSession, JavaSparkContext jsc) {
        JavaPairRDD<CellKey, Long> cellTotalMrCntPairRDD = CellUtils
                .getCellTotalMrCntFromHdfs(jsc, jobInfo.getTaskMonth(), jobInfo.getWorkProvince());
        JavaRDD<CellAndBuilding> cellAndBuildingBeforeFillBackCellTotalMrCntRDD = CellAndBuildingUtils
                .readCellAndBuildingFromHdfs(sparkSession, jsc, jobInfo.getTaskMonth(), jobInfo.getWorkProvince(), indoorCellListBroadcast);

        if (null == cellAndBuildingBeforeFillBackCellTotalMrCntRDD) {
            return false;
        }
        this.cellAndBuildingRDD = cellAndBuildingBeforeFillBackCellTotalMrCntRDD
                .mapToPair((PairFunction<CellAndBuilding, CellKey, CellAndBuilding>) cellAndBuilding ->
                        new Tuple2<>(new CellKey(cellAndBuilding), cellAndBuilding))
                .join(cellTotalMrCntPairRDD)
                .values()
                .map((Function<Tuple2<CellAndBuilding, Long>, CellAndBuilding>) v1 -> {
                    CellAndBuilding cellAndBuilding = v1._1();
                    cellAndBuilding.setCellTotalMrCnt(v1._2());
                    return cellAndBuilding;
                })
                .filter((Function<CellAndBuilding, Boolean>) cellAndBuilding ->
                        cellAndBuilding.getCellTotalMrCnt() > 0);

        cellAndBuildingRDD.persist(StorageLevel.MEMORY_AND_DISK());
        return true;
    }
}
