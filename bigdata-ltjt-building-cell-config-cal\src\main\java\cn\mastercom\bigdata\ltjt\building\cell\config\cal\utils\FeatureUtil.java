package cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils;

import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.feature.BuildingFeature;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.layer.BuildingLayer;
import cn.mastercom.mtcommon.gis.GisConverter;
import cn.mastercom.mtcommon.gis.GisUtils;
import org.apache.spark.broadcast.Broadcast;
import scala.Serializable;
import scala.Tuple2;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class FeatureUtil implements Serializable {
    public static List<Tuple2<BuildingFeature, Integer>> find(boolean isExpand, boolean isGetOne, double longitude, double latitude, int maxExpandDis, Broadcast<BuildingLayer> buildingLayerBroadcast) {
        int lon = GisConverter.d2i(longitude);
        int lat = GisConverter.d2i(latitude);
        return find(isExpand, isGetOne, lon, lat, maxExpandDis, buildingLayerBroadcast);
    }

    public static List<Tuple2<BuildingFeature, Integer>> find(boolean isExpand, boolean isGetOne, int longitude, int latitude, int maxExpandDis, Broadcast<BuildingLayer> buildingLayerBroadcast) {
        List<Tuple2<BuildingFeature, Integer>> resList = new ArrayList<>();
        int realMaxExpandDis = isExpand ? maxExpandDis : 0;
        if (isGetOne) {
            Tuple2<BuildingFeature, Integer> feature = findOneFeature(longitude, latitude, realMaxExpandDis, buildingLayerBroadcast);
            if (null != feature) {
                resList.add(findOneFeature(longitude, latitude, realMaxExpandDis, buildingLayerBroadcast));
            }
        } else {
            List<Tuple2<BuildingFeature, Integer>> tmpList = findNFeature(longitude, latitude, realMaxExpandDis, buildingLayerBroadcast);
            if (null != tmpList && !tmpList.isEmpty()) {
                resList.addAll(tmpList);
            }
        }
        return resList;
    }

    private static List<Tuple2<BuildingFeature, Integer>> findNFeature(int longitude, int latitude, int maxExpandDis, Broadcast<BuildingLayer> buildingLayerBroadcast) {
        List<Tuple2<BuildingFeature, Integer>> resList = new ArrayList<>();
        Set<Integer> areaIdSet = new HashSet<>(); // 使用HashSet提高效率
        if (!WorkUtils.isPositionValid(longitude, latitude)) {
            return resList;
        }

        int currentExpandDis = 0;
        while (currentExpandDis <= maxExpandDis) {
            List<BuildingFeature> featureList = find(longitude, latitude, currentExpandDis, buildingLayerBroadcast);
            if (featureList != null && !featureList.isEmpty()) {
                for (BuildingFeature feature : featureList) {
                    int areaId = feature.getId();
                    if (areaId != -1 && !areaIdSet.contains(areaId)) {
                        areaIdSet.add(areaId);
                        resList.add(new Tuple2<>(feature, currentExpandDis));
                    }
                }
            }
            int expandStep = calExpandStep(currentExpandDis);
            currentExpandDis += expandStep; // 更新搜索距离
        }

        return resList;
    }

    private static Integer calExpandStep(int currentExpandDis) {
        if (currentExpandDis <= 50) {
            return 1;
        } else if (currentExpandDis <= 200) {
            return 3;
        } else {
            return 5;
        }
    }


    private static Tuple2<BuildingFeature, Integer> findOneFeature(int longitude, int latitude, int maxExpandDis, Broadcast<BuildingLayer> buildingLayerBroadcast) {

        if (!WorkUtils.isPositionValid(longitude, latitude)) {
            return null;
        }

        int expandStep = 1;
        int currentExpandDis = 0;
        BuildingFeature nearestFeature = null;

        while (currentExpandDis <= maxExpandDis) {
            List<BuildingFeature> tmpList = find(longitude, latitude, currentExpandDis, buildingLayerBroadcast);
            if (null != tmpList && !tmpList.isEmpty()) {
                int nearestIndex = findNearestFeature(tmpList, longitude, latitude);
                nearestFeature = tmpList.get(nearestIndex);
                break;
            }

            if (currentExpandDis > 50 && currentExpandDis <= 200) {
                expandStep = 3;
            } else if (currentExpandDis > 200) {
                expandStep = 5;
            }
            currentExpandDis += expandStep;
        }

        return null == nearestFeature ? null : new Tuple2<>(nearestFeature, currentExpandDis);
    }

    private static List<BuildingFeature> find(int longitude, int latitude, int expandDis, Broadcast<BuildingLayer> buildingLayerBroadcast) {
        if (0 == expandDis) {
            return buildingLayerBroadcast.value().find(longitude, latitude);
        }
        double tlLongitude = GisConverter.i2d(longitude - GisConverter.convertLongFromMeter(expandDis));
        double tlLatitude = GisConverter.i2d(latitude + GisConverter.convertLatFromMeter(expandDis));
        double brLongitude = GisConverter.i2d(longitude + GisConverter.convertLongFromMeter(expandDis));
        double brLatitude = GisConverter.i2d(latitude - GisConverter.convertLatFromMeter(expandDis));
        return buildingLayerBroadcast.value().find(tlLongitude, tlLatitude, brLongitude, brLatitude);
    }

    private static Integer findNearestFeature(List<BuildingFeature> featureList, int longitude, int latitude) {
        int nearestIndex = -1;
        double nearestDis = Double.MAX_VALUE;
        for (int i = 0; i < featureList.size(); i++) {
            BuildingFeature t = featureList.get(i);
            double featureLon = t.getCenterLongitude();
            double featureLat = t.getCenterLatitude();
            double dis = GisUtils.getDistance(GisConverter.i2d(longitude), GisConverter.i2d(latitude), featureLon, featureLat);
            if (dis < nearestDis) {
                nearestIndex = i;
                nearestDis = dis;
            }
        }
        return nearestIndex;
    }
}