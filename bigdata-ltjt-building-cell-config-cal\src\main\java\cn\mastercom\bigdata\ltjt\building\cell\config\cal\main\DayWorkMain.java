package cn.mastercom.bigdata.ltjt.building.cell.config.cal.main;

import cn.mastercom.bigdata.ltjt.building.cell.config.cal.conf.JobInfo;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.cell.CellConfig;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.layer.BuildingLayer;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils.CellUtils;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils.LayerUtils;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.broadcast.Broadcast;
import org.apache.spark.sql.SparkSession;

import java.util.List;

public class DayWorkMain {

    private final JobInfo jobInfo;

    private Broadcast<List<CellConfig>> indoorCellListBroadcast;
    private Broadcast<BuildingLayer> buildingLayerBroadcast;

    public DayWorkMain(JobInfo jobInfo) {
        this.jobInfo = jobInfo;
    }

    public void work(SparkSession sparkSession, JavaSparkContext jsc) {
        prepare(sparkSession, jsc);

        DayJob dayJob = new DayJob(jobInfo.getTaskDate(), jobInfo.getWorkProvince(), jobInfo.getMaxExpandDis(),
                buildingLayerBroadcast, indoorCellListBroadcast);
        dayJob.work(jsc);
    }

    private void prepare(SparkSession sparkSession, JavaSparkContext jsc) {
        indoorCellListBroadcast = CellUtils
                .getIndoorCellConfigBroadcast(sparkSession, jsc, jobInfo.getWorkProvince());
        buildingLayerBroadcast = LayerUtils
                .getBuildingLayerBroadcast(sparkSession, jsc, jobInfo.getWorkProvince());
    }
}
