package cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.cell;

import cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils.WorkUtils;
import org.apache.spark.sql.Row;

import java.io.Serializable;

public class CellConfig implements Serializable {
    private Integer cityId;
    private Long eci;
    private double longitude;
    private double latitude;

    public CellConfig() {
    }

    public CellConfig(Row row) {
        init(row);
    }

    private void init(Row row) {
        int index = 0;
        this.cityId = row.getInt(index++);
        this.eci = row.getLong(index++);
        this.longitude = row.getDouble(index++);
        this.latitude = row.getDouble(index);
    }

    public boolean valid() {
        return !this.eci.equals(-1L) && WorkUtils.isPositionValid(this.longitude, this.latitude);
    }

    public Integer getCityId() {
        return cityId;
    }

    public Long getEci() {
        return this.eci;
    }

    public double getLongitude() {
        return this.longitude;
    }

    public double getLatitude() {
        return this.latitude;
    }
}