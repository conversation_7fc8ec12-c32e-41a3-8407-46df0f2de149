package cn.mastercom.bigdata.ltjt.building.cell.config.cal.conf;

import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.ProvinceEnum;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.TaskDataTypeEnum;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils.DateUtils;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils.LtjtPathUtils;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils.LtjtProvinceUtils;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils.WorkUtils;
import scala.Serializable;

import java.time.LocalDate;


public class JobInfo implements Serializable {
    public static final String YY_MM_DD = "yyMMdd";
    public static final String YYYY_MM_DD = "yyyyMMdd";
    private static final Integer MAX_EXPAND_DIS = 80;
    private TaskDataTypeEnum taskDataType;
    private String indoorCellBuildingConfigPath;
    private String taskMonth;
    private LocalDate taskDate;
    private ProvinceEnum workProvince;

    public boolean init(String[] args) {
        if (!dealArgs(args)) {
            return false;
        }
        this.indoorCellBuildingConfigPath = LtjtPathUtils.getIndoorCellBuildingConfigPath();
        return true;
    }

    public boolean dealArgs(String[] args) {
        this.taskDataType = TaskDataTypeEnum.getTaskDataType(args[0]);
        if (this.taskDataType.equals(TaskDataTypeEnum.DAY)) {
            this.taskDate = DateUtils.getDateByString(args[1], JobInfo.YY_MM_DD);
            this.taskMonth = "";
        } else if (this.taskDataType.equals(TaskDataTypeEnum.MONTH)) {
            this.taskMonth = args[1];
            this.taskDate = DateUtils.getDateByString(args[1] + "01", JobInfo.YY_MM_DD);
        } else {
            return false;
        }
        this.workProvince = LtjtProvinceUtils.getProvinceByLtjtId(WorkUtils.stringToInt(args[2], -1));
        return !this.workProvince.equals(ProvinceEnum.OTHER);
    }

    public ProvinceEnum getWorkProvince() {
        return this.workProvince;
    }

    public Integer getMaxExpandDis() {
        return MAX_EXPAND_DIS;
    }

    public LocalDate getTaskDate() {
        return taskDate;
    }

    public String getTaskMonth() {
        return taskMonth;
    }

    public String getIndoorCellBuildingConfigPath() {
        return this.indoorCellBuildingConfigPath;
    }

    public TaskDataTypeEnum getTaskDataType() {
        return taskDataType;
    }
}