package cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.data;

import cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils.WorkUtils;

import java.io.Serializable;

public class MdtSample implements Serializable {
    private static final String SPLIT = ",";
    private Long eci;
    private Double longitude;
    private Double latitude;
    private Long tadv;

    public MdtSample() {
    }

    public MdtSample(String line) {
        init(line);
    }

    private void init(String line) {
        String[] itemList = line.split(SPLIT);

        try {
            this.eci = WorkUtils.stringToLong(itemList[6], -1L);
            this.longitude = WorkUtils.stringToDouble(itemList[63], -1.0d);
            this.latitude = WorkUtils.stringToDouble(itemList[64], -1.0d);
            this.tadv = WorkUtils.stringToLong(itemList[59], -1);
        } catch (Exception e) {
            this.eci = -1L;
        }
    }

    public boolean valid() {
        return this.eci > 0 && WorkUtils.isPositionValid(this.longitude, this.latitude);
    }

    public boolean isMeetsTadvThreshold() {
        return this.tadv <= 2 && this.tadv >= 0;
    }

    public Long getEci() {
        return this.eci;
    }

    public Double getLongitude() {
        return this.longitude;
    }

    public Double getLatitude() {
        return this.latitude;
    }
}