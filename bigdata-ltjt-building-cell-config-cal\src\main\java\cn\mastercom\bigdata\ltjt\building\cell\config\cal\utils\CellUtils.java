package cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils;

import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.ProvinceEnum;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.cell.CellConfig;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.data.CellAndBuilding;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.key.CellKey;
import org.apache.spark.api.java.JavaPairRDD;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.api.java.function.Function;
import org.apache.spark.api.java.function.Function2;
import org.apache.spark.api.java.function.PairFunction;
import org.apache.spark.broadcast.Broadcast;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import scala.Serializable;
import scala.Tuple2;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CellUtils implements Serializable {
    private static final Logger logger = LoggerFactory.getLogger(CellUtils.class);

    private CellUtils() {
        throw new IllegalStateException("Utility class");
    }

    public static Broadcast<List<CellConfig>> getIndoorCellConfigBroadcast(SparkSession sparkSession, JavaSparkContext jsc, ProvinceEnum workProvince) {
        String sql = LtjtPathUtils.getIndoorCellSql(workProvince);
        logger.info("=== 开始读取工参配置，sql 语句为： {} ===", sql);
        JavaRDD<Row> rawDataRDD = SourceUtils.read(sparkSession, sql, "cell-config");
        if (null == rawDataRDD) {
            logger.error("=== 读取工参失败 ===");
            return null;
        }

        List<CellConfig> cellConfigList = rawDataRDD
                .map((Function<Row, CellConfig>) CellConfig::new)
                .filter((Function<CellConfig, Boolean>) CellConfig::valid)
                .collect();
        logger.info("=== 读取工参配置结束，共读取{}条工参记录 ===", cellConfigList.size());
        return jsc.broadcast(cellConfigList);
    }

    public static List<CellKey> getAllEci(List<CellConfig> cellConfigList) {
        List<CellKey> eciList = new ArrayList<>();
        for (CellConfig cellConfig : cellConfigList) {
            eciList.add(new CellKey(cellConfig));
        }
        return eciList;
    }

    public static Map<CellKey, CellConfig> getEciCellConfigMap(List<CellConfig> cellConfigList) {
        HashMap<CellKey, CellConfig> eciCellConfigMap = new HashMap<>();
        for (CellConfig cellConfig : cellConfigList) {
            eciCellConfigMap.put(new CellKey(cellConfig), cellConfig);
        }
        return eciCellConfigMap;
    }

    public static JavaPairRDD<CellKey, Long> getCellTotalMrCntFromHdfs(JavaSparkContext jsc,
                                                                       String taskMonth, ProvinceEnum workProvince) {
        String dataPath = LtjtPathUtils.getOutputPath("cell-total-mrcnt", taskMonth, workProvince);
        JavaRDD<String> rawDataRDD = SourceUtils.read(jsc, dataPath, "monthly-cell-total-mrcnt");
        if (null == rawDataRDD) {
            return null;
        }

        return rawDataRDD
                .mapToPair((PairFunction<String, CellKey, Long>) s -> {
                    String[] itemList = s.split("\t");
                    Long eci = WorkUtils.stringToLong(itemList[0], -1L);
                    Long mrCnt = WorkUtils.stringToLong(itemList[1], -1L);
                    return new Tuple2<>(new CellKey(eci), mrCnt);
                })
                .filter((Function<Tuple2<CellKey, Long>, Boolean>) v1 ->
                        v1._1().valid() && v1._2() > 0)
                .reduceByKey((Function2<Long, Long, Long>) (v1, v2) -> {
                    v1 += v2;
                    return v1;
                });
    }

    /**
     * 获取按采样点个数倒序排序的前90%数量的小区键集合
     *
     * <p>方法执行流程：
     * <ol>
     *   <li>将输入RDD中的小区按键关联的采样点个数进行降序排序</li>
     *   <li>计算应选取的小区数量：总小区数 × 90%（四舍五入或向上取整，需在实现中明确）</li>
     *   <li>保留排序结果中的前N个小区（N=总小区数×90%）</li>
     *   <li>返回被选取的小区键集合</li>
     * </ol>
     *
     * <p>边界条件处理：
     * <ul>
     *   <li>空集合输入将返回空RDD</li>
     *   <li>当仅有一个小区时，总是包含该小区</li>
     *   <li>百分比计算策略：在实现中必须明确处理小数时的规则（如Math.ceil或四舍五入）</li>
     *   <li>当计算结果N超过实际数量时（如输入10个小区按90%需取9个），按实际数量返回</li>
     * </ul>
     *
     * @param cellAndBuildingRDD 包含小区键和对应采样数数量的原始数据集，要求：
     *                           - 每个记录的采样数应为非负值
     *                           - 采样数可为零，但会影响排序位置
     * @return 新的JavaRDD<CellKey>，包含：
     * - 严格按采样数倒序排列的前90%数量小区键
     * - 数量计算结果遵循实现定义的取舍规则
     * @throws NullPointerException 当输入参数为null时抛出
     */
    public static JavaRDD<CellKey> getCellKeysInTop90PercentileBySampling(JavaRDD<CellAndBuilding> cellAndBuildingRDD) {
        JavaPairRDD<CellKey, Long> eciToSampleCntPairRDD = cellAndBuildingRDD
                .mapToPair((PairFunction<CellAndBuilding, CellKey, Long>) cellAndBuilding ->
                        new Tuple2<>(new CellKey(cellAndBuilding), cellAndBuilding.getCellTotalMrCnt()))
                .reduceByKey((Function2<Long, Long, Long>) (v1, v2) -> v1);
        // 降序排序
        List<Tuple2<CellKey, Long>> eciToSampleCntList = eciToSampleCntPairRDD
                .mapToPair((PairFunction<Tuple2<CellKey, Long>, Long, CellKey>) tuple2 ->
                        new Tuple2<>(tuple2._2(), tuple2._1()))
                .sortByKey(false)
                .map((Function<Tuple2<Long, CellKey>, Tuple2<CellKey, Long>>) tuple2 ->
                        new Tuple2<>(tuple2._2(), tuple2._1()))
                .collect();
        // 过滤掉排名在90%以后的小区
        int requireIndex = (int) Math.ceil(eciToSampleCntList.size() * 90 / 100.0) - 1;
        long sampleCntThreshold = eciToSampleCntList.get(requireIndex)._2();
        return eciToSampleCntPairRDD
                .filter((Function<Tuple2<CellKey, Long>, Boolean>) v1 ->
                        v1._2() >= sampleCntThreshold)
                .map((Function<Tuple2<CellKey, Long>, CellKey>) Tuple2::_1);
    }


}