<mxfile host="Electron" modified="2025-01-02T09:46:37.292Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.2.5 Chrome/120.0.6099.109 Electron/28.1.0 Safari/537.36" etag="StAVPZO5qFNaQgrxhUoC" version="24.2.5" type="device" pages="2">
  <diagram name="第 1 页" id="Gl8U5HqfLu4_IYvCTifV">
    <mxGraphModel dx="1430" dy="826" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="QHrWRbxfoJhmZiQTFwwY-2" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="MEJKfUfqNdN42AETWuXO-1" target="QHrWRbxfoJhmZiQTFwwY-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-1" value="提取室分小区MDT月粒度采样点数据" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="409" y="460" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-2" value="室分楼宇配置表" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="409" y="120" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-3" value="楼宇图层位置" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="409" y="220" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-4" value="有室分楼宇" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="279" y="320" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-6" value="无室分楼宇" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="529" y="320" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-8" value="有室分楼宇&lt;br&gt;有室分MDT采样点" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="189" y="590" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-10" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-4" target="MEJKfUfqNdN42AETWuXO-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="459" y="630" as="sourcePoint" />
            <mxPoint x="509" y="580" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-11" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-6" target="MEJKfUfqNdN42AETWuXO-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="459" y="630" as="sourcePoint" />
            <mxPoint x="509" y="580" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-13" value="无室分楼宇&lt;br style=&quot;border-color: var(--border-color);&quot;&gt;有室分MDT采样点" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="499" y="590" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-14" value="无室分楼宇&lt;br style=&quot;border-color: var(--border-color);&quot;&gt;无室分MDT采样点" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="659" y="590" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-15" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-1" target="MEJKfUfqNdN42AETWuXO-8" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="459" y="710" as="sourcePoint" />
            <mxPoint x="509" y="660" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-17" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" target="MEJKfUfqNdN42AETWuXO-13" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="469" y="520" as="sourcePoint" />
            <mxPoint x="509" y="660" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-18" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-1" target="MEJKfUfqNdN42AETWuXO-14" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="459" y="710" as="sourcePoint" />
            <mxPoint x="509" y="660" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-20" value="不考虑" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="659" y="680" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-22" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-14" target="MEJKfUfqNdN42AETWuXO-20" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="459" y="610" as="sourcePoint" />
            <mxPoint x="509" y="560" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-24" value="楼宇内室分小区MDT采样点数统计" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="189" y="820" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-27" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-24" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="459" y="960" as="sourcePoint" />
            <mxPoint x="249" y="940" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-29" value="楼宇室分小区采样点数是否大于平均值" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="189" y="1050" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-30" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="249" y="1000" as="sourcePoint" />
            <mxPoint x="249" y="1050" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-32" value="剔除楼宇采样点TA大于2的采样点" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="189" y="710" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-33" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-8" target="MEJKfUfqNdN42AETWuXO-32" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="459" y="930" as="sourcePoint" />
            <mxPoint x="509" y="880" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-34" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-32" target="MEJKfUfqNdN42AETWuXO-24" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="459" y="910" as="sourcePoint" />
            <mxPoint x="509" y="860" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-36" value="取楼宇内室分小区采样点数&lt;font color=&quot;#ff0000&quot;&gt;最大值&lt;/font&gt;作为本期楼宇主室分小区" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="110" y="1160" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-37" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-29" target="MEJKfUfqNdN42AETWuXO-36" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="459" y="1300" as="sourcePoint" />
            <mxPoint x="509" y="1250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-38" value="楼宇内室分小区MDT采样点数统计" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="509" y="820" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-40" value="楼宇内室分小区MDT采样点数统计" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="509" y="920" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-41" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-38" target="MEJKfUfqNdN42AETWuXO-40" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="779" y="960" as="sourcePoint" />
            <mxPoint x="829" y="910" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-44" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="88OCIqy8_4XKkSVlLQuI-2" target="SlegZ9OdaJBCwldhEJD1-15" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="779" y="1140" as="sourcePoint" />
            <mxPoint x="569" y="1050" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-45" value="剔除楼宇采样点TA大于2的采样点" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="509" y="710" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-46" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-45" target="MEJKfUfqNdN42AETWuXO-38" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="779" y="910" as="sourcePoint" />
            <mxPoint x="829" y="860" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-50" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="559" y="650" as="sourcePoint" />
            <mxPoint x="559" y="710" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-51" value="界面显示样式：&lt;br&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="34" y="1620" width="760" height="40" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-52" value="用户修改策略：" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="34" y="1731" width="760" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-2" value="室分小区" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="329" y="1901" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-3" value="小区覆盖楼宇数统计" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="329" y="2001" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-4" value="1个" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="169" y="2091" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-5" value="多个" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="319" y="2091" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-6" value="所有采样点均落在对应楼宇内" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="169" y="2211" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-7" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-4" target="dTuDczm5kZ5u2p71eDUZ-6" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="429" y="2261" as="sourcePoint" />
            <mxPoint x="479" y="2211" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-8" value="有对应关系楼宇采样点" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="189" y="2321" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-9" value="无对应关系楼宇的采样点" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="319" y="2321" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-12" value="吸附周边50米内采样点，吸附原则：距离哪个楼宇边框近属于哪个楼宇" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="189" y="2421" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-13" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-8" target="dTuDczm5kZ5u2p71eDUZ-12" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="429" y="2351" as="sourcePoint" />
            <mxPoint x="479" y="2301" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-14" value="采样点与楼宇进行关联匹配" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="319" y="2211" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-15" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-5" target="dTuDczm5kZ5u2p71eDUZ-14" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="429" y="2231" as="sourcePoint" />
            <mxPoint x="479" y="2181" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-16" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-14" target="dTuDczm5kZ5u2p71eDUZ-8" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="429" y="2231" as="sourcePoint" />
            <mxPoint x="479" y="2181" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-17" value="其他未落在楼宇图层上的采样点" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="449" y="2321" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-18" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" target="dTuDczm5kZ5u2p71eDUZ-9" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="379" y="2271" as="sourcePoint" />
            <mxPoint x="479" y="2181" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-19" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-14" target="dTuDczm5kZ5u2p71eDUZ-17" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="429" y="2231" as="sourcePoint" />
            <mxPoint x="479" y="2181" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-21" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-9" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="429" y="2221" as="sourcePoint" />
            <mxPoint x="429" y="2421" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-22" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-17" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="429" y="2221" as="sourcePoint" />
            <mxPoint x="429" y="2421" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-24" value="有关楼宇指标的统计，这些采样点丢弃，不计算到楼宇指标内。" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="369" y="2421" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-27" value="无" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="489" y="2091" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-29" value="先按照LGB进行落位置" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="488" y="2171" width="121" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-30" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-27" target="dTuDczm5kZ5u2p71eDUZ-29" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="429" y="2311" as="sourcePoint" />
            <mxPoint x="479" y="2261" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-31" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-2" target="dTuDczm5kZ5u2p71eDUZ-3" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="429" y="2011" as="sourcePoint" />
            <mxPoint x="479" y="1961" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-32" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.45;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-3" target="dTuDczm5kZ5u2p71eDUZ-5" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="429" y="2011" as="sourcePoint" />
            <mxPoint x="479" y="1961" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-33" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-3" target="dTuDczm5kZ5u2p71eDUZ-4" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="429" y="2011" as="sourcePoint" />
            <mxPoint x="479" y="1961" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-34" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-3" target="dTuDczm5kZ5u2p71eDUZ-27" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="429" y="2011" as="sourcePoint" />
            <mxPoint x="479" y="1961" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="SlegZ9OdaJBCwldhEJD1-6" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-3" target="MEJKfUfqNdN42AETWuXO-4" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="319" y="560" as="sourcePoint" />
            <mxPoint x="369" y="510" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="SlegZ9OdaJBCwldhEJD1-7" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" target="MEJKfUfqNdN42AETWuXO-6" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="469" y="280" as="sourcePoint" />
            <mxPoint x="349" y="330" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="SlegZ9OdaJBCwldhEJD1-8" value="楼宇与室分小区对应算法：&lt;br&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="40" y="20" width="758" height="50" as="geometry" />
        </mxCell>
        <mxCell id="SlegZ9OdaJBCwldhEJD1-9" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-2" target="MEJKfUfqNdN42AETWuXO-3" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="510" y="290" as="sourcePoint" />
            <mxPoint x="590" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="SlegZ9OdaJBCwldhEJD1-11" value="&lt;span style=&quot;color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;&quot;&gt;楼宇主室分小区：楼宇内室分小区采样频次占比最大值对应的小区&lt;/span&gt;&lt;br style=&quot;border-color: var(--border-color); color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;&quot;&gt;&lt;div style=&quot;border-color: var(--border-color); color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; text-align: left;&quot;&gt;&lt;span style=&quot;border-color: var(--border-color); background-color: initial;&quot;&gt;楼宇辅室分小区：辅室分小区&lt;br&gt;注：用户可以自己进行修改。&lt;/span&gt;&lt;/div&gt;" style="text;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="69" y="1670" width="700" height="50" as="geometry" />
        </mxCell>
        <mxCell id="SlegZ9OdaJBCwldhEJD1-12" value="&lt;span style=&quot;color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;&quot;&gt;1、允许特定用户对楼宇对应室分关系进行增加、删除操作。&lt;br&gt;&lt;/span&gt;2、&lt;span style=&quot;color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;&quot;&gt;对用户增加的小区，删除的小区，形成一张用户操作记录表，表内有楼宇ID、小区主次，小区ECI、增加/删除、时间&lt;/span&gt;" style="text;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="79.5" y="1791" width="651" height="40" as="geometry" />
        </mxCell>
        <mxCell id="SlegZ9OdaJBCwldhEJD1-13" value="取楼宇内室分小区采样点数&lt;font color=&quot;#ff3333&quot;&gt;大于平均值小于最大值&lt;/font&gt;作为楼宇本期辅室分小区" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="250" y="1160" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="SlegZ9OdaJBCwldhEJD1-14" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-29" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="430" y="1330" as="sourcePoint" />
            <mxPoint x="320" y="1160" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="SlegZ9OdaJBCwldhEJD1-15" value="取楼宇内室分小区采样点占比&lt;font color=&quot;#ff0000&quot;&gt;最大值&lt;/font&gt;作为&lt;font color=&quot;#3399ff&quot;&gt;本期&lt;/font&gt;楼宇主室分小区" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="430" y="1110" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="SlegZ9OdaJBCwldhEJD1-16" value="有楼宇与室分小区对应关系后的采样点与楼宇匹配策略" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="34" y="1831" width="760" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Twr2VkyiY78_Iyu74IA3-1" value="小区对应多个楼宇时，" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="359" y="1270" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Twr2VkyiY78_Iyu74IA3-2" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-36" target="Twr2VkyiY78_Iyu74IA3-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="1450" as="sourcePoint" />
            <mxPoint x="440" y="1400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Twr2VkyiY78_Iyu74IA3-3" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="SlegZ9OdaJBCwldhEJD1-13" target="Twr2VkyiY78_Iyu74IA3-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="1450" as="sourcePoint" />
            <mxPoint x="440" y="1400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Twr2VkyiY78_Iyu74IA3-6" value="采样点占比小剔除：剔除落到楼宇内小区采样点数占小区总采样点比例少于20%的楼宇" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="334" y="1370" width="170" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Twr2VkyiY78_Iyu74IA3-7" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="Twr2VkyiY78_Iyu74IA3-1" target="Twr2VkyiY78_Iyu74IA3-6" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="1240" as="sourcePoint" />
            <mxPoint x="440" y="1190" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Twr2VkyiY78_Iyu74IA3-8" value="所有采样点均落在室分小区位置" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="489" y="2251" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Twr2VkyiY78_Iyu74IA3-12" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-29" target="Twr2VkyiY78_Iyu74IA3-8" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="420" y="2241" as="sourcePoint" />
            <mxPoint x="470" y="2191" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="v47DIhSJnMT9DxbWAAaH-1" value="取楼宇内室分小区采样点占比&lt;font style=&quot;border-color: var(--border-color);&quot; color=&quot;#ff3333&quot;&gt;大于平均值小于最大值&lt;/font&gt;作为楼宇&lt;font color=&quot;#3399ff&quot;&gt;保留&lt;/font&gt;辅室分小区" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="590" y="1100" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="v47DIhSJnMT9DxbWAAaH-2" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="88OCIqy8_4XKkSVlLQuI-2" target="v47DIhSJnMT9DxbWAAaH-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="570" y="1000" as="sourcePoint" />
            <mxPoint x="539" y="1060" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="v47DIhSJnMT9DxbWAAaH-3" value="&lt;font color=&quot;#ff6666&quot;&gt;下期如果保留的小区楼宇室分小区采样点占比大于平均值则作为下一起楼宇辅室分小区&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontColor=#3399FF;" parent="1" vertex="1">
          <mxGeometry x="590" y="1180" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="v47DIhSJnMT9DxbWAAaH-4" value="" style="endArrow=classic;html=1;rounded=0;fontColor=#FF6666;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="v47DIhSJnMT9DxbWAAaH-1" target="v47DIhSJnMT9DxbWAAaH-3" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="1110" as="sourcePoint" />
            <mxPoint x="440" y="1060" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="v47DIhSJnMT9DxbWAAaH-5" value="" style="endArrow=classic;html=1;rounded=0;fontColor=#FF6666;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="SlegZ9OdaJBCwldhEJD1-15" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="1110" as="sourcePoint" />
            <mxPoint x="420" y="1270" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="v47DIhSJnMT9DxbWAAaH-6" value="" style="endArrow=classic;html=1;rounded=0;fontColor=#FF6666;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="v47DIhSJnMT9DxbWAAaH-3" target="Twr2VkyiY78_Iyu74IA3-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="1110" as="sourcePoint" />
            <mxPoint x="440" y="1060" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="BKGFdhFcGff8IqCUX1Js-1" value="楼间距异常剔除：剔除小区覆盖下属楼宇，相互间距离最小值大于500米的楼宇" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="339" y="1470" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="BKGFdhFcGff8IqCUX1Js-2" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" target="BKGFdhFcGff8IqCUX1Js-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="419" y="1430" as="sourcePoint" />
            <mxPoint x="419.4000000000001" y="1429" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="88OCIqy8_4XKkSVlLQuI-1" value="楼宇内室分小区MDT采样点数统计" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="189" y="940" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="88OCIqy8_4XKkSVlLQuI-2" value="楼宇室分小区采样点数是否大于平均值" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="509" y="1020" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="88OCIqy8_4XKkSVlLQuI-7" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="568.43" y="980" as="sourcePoint" />
            <mxPoint x="568.43" y="1020" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="QHrWRbxfoJhmZiQTFwwY-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="QHrWRbxfoJhmZiQTFwwY-1" target="QHrWRbxfoJhmZiQTFwwY-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="QHrWRbxfoJhmZiQTFwwY-1" value="无室分楼宇&lt;br style=&quot;border-color: var(--border-color);&quot;&gt;无室分MDT采样点" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="354" y="590" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="QHrWRbxfoJhmZiQTFwwY-3" value="不考虑" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="354" y="690" width="120" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="BHEEye47jg_9a50YjLHO" name="第 2 页">
    <mxGraphModel dx="1728" dy="998" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="albcE6jhP9YvvwfOzbzl-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="jLy5Qbelym_fzd4RW6AO-1" target="jLy5Qbelym_fzd4RW6AO-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="jLy5Qbelym_fzd4RW6AO-1" value="室分楼宇配置表" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="210" y="120" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="htk9kU5CBMiG8TAa8aaG-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="jLy5Qbelym_fzd4RW6AO-4" target="albcE6jhP9YvvwfOzbzl-4">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="270" y="820" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="jLy5Qbelym_fzd4RW6AO-4" value="楼宇id+是否有室分覆盖配置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="210" y="320" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="jLy5Qbelym_fzd4RW6AO-10" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="jLy5Qbelym_fzd4RW6AO-8" target="jLy5Qbelym_fzd4RW6AO-9" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="jLy5Qbelym_fzd4RW6AO-8" value="mdt采样点(整月)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="570" y="110" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="utr8etj-6jkVzBQ4fML0-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="jLy5Qbelym_fzd4RW6AO-9" target="utr8etj-6jkVzBQ4fML0-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="jLy5Qbelym_fzd4RW6AO-9" value="经纬度+eci" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="570" y="210" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="jLy5Qbelym_fzd4RW6AO-11" value="过滤非室分小区与TA大于2" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="620" y="180" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="utr8etj-6jkVzBQ4fML0-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="jLy5Qbelym_fzd4RW6AO-16" target="utr8etj-6jkVzBQ4fML0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="435" y="290" />
              <mxPoint x="630" y="290" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="albcE6jhP9YvvwfOzbzl-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="jLy5Qbelym_fzd4RW6AO-16" target="jLy5Qbelym_fzd4RW6AO-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="jLy5Qbelym_fzd4RW6AO-16" value="楼宇图层" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="400" y="200" width="70" height="30" as="geometry" />
        </mxCell>
        <mxCell id="sXCLjzXY6jWjYLR0dM1H-78" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="utr8etj-6jkVzBQ4fML0-1" target="sXCLjzXY6jWjYLR0dM1H-76" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="utr8etj-6jkVzBQ4fML0-1" value="楼宇id+eci+采样点个数" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="570" y="320" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="sXCLjzXY6jWjYLR0dM1H-72" target="sXCLjzXY6jWjYLR0dM1H-80" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="sXCLjzXY6jWjYLR0dM1H-72" target="sXCLjzXY6jWjYLR0dM1H-79" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="sXCLjzXY6jWjYLR0dM1H-72" value="统计楼宇内不同室分小区采样点占比" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="570" y="555" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="sXCLjzXY6jWjYLR0dM1H-75" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="sXCLjzXY6jWjYLR0dM1H-76" target="sXCLjzXY6jWjYLR0dM1H-72" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="sXCLjzXY6jWjYLR0dM1H-76" value="按楼宇id进行汇聚" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="570" y="430" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MATF-sM5usYceBuwxZyg-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="sXCLjzXY6jWjYLR0dM1H-79" target="ZiUwMDU_Nl_DjrNQZuaP-21" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="730" y="1140" />
              <mxPoint x="540" y="1140" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="sXCLjzXY6jWjYLR0dM1H-79" value="采样点数&lt;font color=&quot;#ff0000&quot;&gt;最大值&lt;/font&gt;的室分小区作为本期楼宇&lt;font color=&quot;#ff0000&quot;&gt;主室分小区&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="670" y="680" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="albcE6jhP9YvvwfOzbzl-5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="sXCLjzXY6jWjYLR0dM1H-80" target="albcE6jhP9YvvwfOzbzl-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="sXCLjzXY6jWjYLR0dM1H-80" value="采样点数&lt;font color=&quot;#ff3333&quot;&gt;大于平均值&lt;/font&gt;的室分小区列表" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="460" y="680" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MATF-sM5usYceBuwxZyg-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="sXCLjzXY6jWjYLR0dM1H-83" target="ZiUwMDU_Nl_DjrNQZuaP-21" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="610" y="1130" />
              <mxPoint x="540" y="1130" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="sXCLjzXY6jWjYLR0dM1H-83" value="楼宇本期辅室分小区" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="550" y="915" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="albcE6jhP9YvvwfOzbzl-4" target="sXCLjzXY6jWjYLR0dM1H-83" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="520" y="890" />
              <mxPoint x="610" y="890" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-19" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZiUwMDU_Nl_DjrNQZuaP-17" vertex="1" connectable="0">
          <mxGeometry x="-0.0134" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="albcE6jhP9YvvwfOzbzl-4" target="MATF-sM5usYceBuwxZyg-6" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="393" y="910" as="targetPoint" />
            <Array as="points">
              <mxPoint x="520" y="890" />
              <mxPoint x="393" y="890" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-20" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZiUwMDU_Nl_DjrNQZuaP-18" vertex="1" connectable="0">
          <mxGeometry x="0.0731" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="albcE6jhP9YvvwfOzbzl-4" value="是否有室分配置" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="445" y="780" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-27" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ZiUwMDU_Nl_DjrNQZuaP-21" target="ZiUwMDU_Nl_DjrNQZuaP-26" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-21" value="楼宇id+eci+role(主室分小区，辅室分小区，保留辅室分小区)+采样点个数" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="437.5" y="1160" width="205" height="70" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-29" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ZiUwMDU_Nl_DjrNQZuaP-26" target="ZiUwMDU_Nl_DjrNQZuaP-28" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-26" value="按eci进行分组" style="whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="480" y="1280" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-34" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="ZiUwMDU_Nl_DjrNQZuaP-28" target="ZiUwMDU_Nl_DjrNQZuaP-42" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="625" y="1560" as="targetPoint" />
            <Array as="points">
              <mxPoint x="540" y="1510" />
              <mxPoint x="630" y="1510" />
              <mxPoint x="630" y="1830" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-35" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZiUwMDU_Nl_DjrNQZuaP-34" vertex="1" connectable="0">
          <mxGeometry x="0.0772" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-37" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ZiUwMDU_Nl_DjrNQZuaP-28" target="ZiUwMDU_Nl_DjrNQZuaP-36" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-38" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZiUwMDU_Nl_DjrNQZuaP-37" vertex="1" connectable="0">
          <mxGeometry x="0.2398" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-28" value="同一个eci是否有多条数据(&lt;div&gt;即小区是否对应多个楼宇)&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="395" y="1380" width="290" height="80" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-40" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ZiUwMDU_Nl_DjrNQZuaP-36" target="ZiUwMDU_Nl_DjrNQZuaP-39" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-41" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZiUwMDU_Nl_DjrNQZuaP-40" vertex="1" connectable="0">
          <mxGeometry x="-0.0714" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-36" value="落到楼宇内小区采样点数占&lt;div&gt;小区总采样点比例是否大于等于20%&lt;br&gt;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="250" y="1560" width="290" height="80" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-43" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ZiUwMDU_Nl_DjrNQZuaP-39" target="ZiUwMDU_Nl_DjrNQZuaP-42" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-44" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZiUwMDU_Nl_DjrNQZuaP-43" vertex="1" connectable="0">
          <mxGeometry x="-0.1857" y="3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-39" value="到其他楼宇的距离是否均大于500米" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="250" y="1680" width="290" height="80" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-42" value="输出结果" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="335" y="1800" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MATF-sM5usYceBuwxZyg-8" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="MATF-sM5usYceBuwxZyg-6" target="MATF-sM5usYceBuwxZyg-7" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MATF-sM5usYceBuwxZyg-11" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="MATF-sM5usYceBuwxZyg-8" vertex="1" connectable="0">
          <mxGeometry x="0.1503" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MATF-sM5usYceBuwxZyg-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="MATF-sM5usYceBuwxZyg-6" target="MATF-sM5usYceBuwxZyg-9" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MATF-sM5usYceBuwxZyg-12" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="MATF-sM5usYceBuwxZyg-10" vertex="1" connectable="0">
          <mxGeometry x="0.0085" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MATF-sM5usYceBuwxZyg-6" value="上一期是否为&lt;div&gt;保留辅室分小区&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="320" y="905" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="MATF-sM5usYceBuwxZyg-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="MATF-sM5usYceBuwxZyg-7" target="ZiUwMDU_Nl_DjrNQZuaP-21" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="320" y="1120" />
              <mxPoint x="540" y="1120" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="MATF-sM5usYceBuwxZyg-7" value="楼宇本期辅室分小区" style="whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="260" y="1030" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MATF-sM5usYceBuwxZyg-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="MATF-sM5usYceBuwxZyg-9" target="ZiUwMDU_Nl_DjrNQZuaP-21" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MATF-sM5usYceBuwxZyg-9" value="楼宇本期&lt;font color=&quot;#3399ff&quot;&gt;保留&lt;/font&gt;辅室分小区" style="whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="420" y="1030" width="120" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
