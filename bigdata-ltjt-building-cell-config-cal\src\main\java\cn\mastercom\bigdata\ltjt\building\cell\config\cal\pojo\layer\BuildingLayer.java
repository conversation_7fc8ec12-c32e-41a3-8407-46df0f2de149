package cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.layer;

import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.feature.BuildingFeature;
import cn.mastercom.mtcommon.gis.booster.BoosterFactory;
import org.apache.spark.sql.Row;

import java.util.ArrayList;
import java.util.List;

public class BuildingLayer extends LayerBase<BuildingFeature> {
    public BuildingLayer() {
        super(BoosterFactory.newFindDPolyBooster(200), "building");
    }

    @Override
    public BuildingFeature fromRow(Row row) {
        BuildingFeature buildingFeature = new BuildingFeature(row);
        if (buildingFeature.valid()) {
            return buildingFeature;
        }
        return null;
    }

    @Override
    protected List<BuildingFeature> doIntersects(List<BuildingFeature> ls, double longitude, double latitude) {
        List<BuildingFeature> result = new ArrayList<>();
        for (BuildingFeature buildingFeature : ls) {
            if (buildingFeature.intersects(longitude, latitude)) {
                result.add(buildingFeature);
            }
        }
        return result;
    }

    @Override
    protected List<BuildingFeature> doIntersects(List<BuildingFeature> ls, double tlLongitude, double tlLatitude, double brLongitude, double brLatitude) {
        List<BuildingFeature> result = new ArrayList<>();
        for (BuildingFeature buildingFeature : ls) {
            if (buildingFeature.intersects(tlLongitude, tlLatitude, brLongitude, brLatitude)) {
                result.add(buildingFeature);
            }
        }
        return result;
    }

    @Override
    public boolean valid(BuildingFeature buildingFeature) {
        return buildingFeature.valid();
    }
}