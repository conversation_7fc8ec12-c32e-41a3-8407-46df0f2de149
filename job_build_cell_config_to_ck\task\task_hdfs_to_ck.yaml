reader:
  parameter:
    - path: hdfs://hdfsunity/data/wndoidAdmin_yuhui/hive/warehouse/ads_wndoidadmin_yuhui.db/config/${provinceName}/building-cell-config/${date,format=yyMM}/tb_building_cell_config
      column: city_id int, building_id int, building_name string, building_center_longitude int, building_center_latitude int, eci long, cell_longitude int, cell_latitude int, mr_cnt long, cell_total_mr_cnt long, cell_role string, is_building_indoor_coverage string
      format: csv
      fieldDelimiter: \t
      asTableName: data

processor:
  parameter:
    - sql: " SELECT '${date,format=yyMM}' AS data_time,
        CAST('${provId_standard}' AS int) AS province_id,
        city_id,
        building_id,
        building_name,
        building_center_longitude,
        building_center_latitude,
        eci,
        cell_longitude,
        cell_latitude,
        mr_cnt,
        cell_total_mr_cnt,
        CASE
        WHEN cell_role = 'mainIndoorCell' THEN '主室分小区'
        WHEN cell_role = 'auxiliaryIndoorCell' THEN '辅室分小区'
        WHEN cell_role = 'alternativeAuxiliaryIndoorCell' THEN '保留辅室分小区'
        WHEN cell_role = 'otherCell-A' THEN '其他小区-A'
        WHEN cell_role = 'otherCell-B' THEN '其他小区-B'
        WHEN cell_role = 'otherCell-C' THEN '其他小区-C'
        WHEN cell_role = 'otherCell-D' THEN '其他小区-D'
        WHEN cell_role = 'otherCell-E' THEN '其他小区-E'
        WHEN cell_role = 'otherCell-F' THEN '其他小区-F'
        WHEN cell_role = 'lowConfidenceMainIndoorCell-A' THEN '存疑主室分小区A'
        WHEN cell_role = 'lowConfidenceAuxiliaryIndoorCell-A' THEN '存疑辅室分小区A'
        WHEN cell_role = 'lowConfidenceAlternativeAuxiliaryIndoorCell-A' THEN '存疑保留辅室分小区A'
        WHEN cell_role = 'lowConfidenceMainIndoorCell-B' THEN '存疑主室分小区B'
        WHEN cell_role = 'lowConfidenceAuxiliaryIndoorCell-B' THEN '存疑辅室分小区B'
        WHEN cell_role = 'lowConfidenceAlternativeAuxiliaryIndoorCell-B' THEN '存疑保留辅室分小区B'
        ELSE cell_role
        END AS cell_role,
        if(is_building_indoor_coverage = 'true', '是', '否') AS is_building_indoor_coverage
        FROM data "
      asTableName: tmp1

writer:
  parameter:
    tableName: tb_ads_mtbd_building_cell_config_m_cluster
    jdbc: *********************************************************************************************************************************************************
    user: wuyunxiao
    password: pm3jHw8rZhJy
    writeMode: overwrite
    parallelism: 20
    cluster: mt-prod-ck
