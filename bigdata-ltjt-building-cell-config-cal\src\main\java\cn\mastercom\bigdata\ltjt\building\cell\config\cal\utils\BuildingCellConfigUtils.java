package cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils;

import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.CellRoleEnum;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.ProvinceEnum;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.data.CellAndBuilding;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.key.BuildingKey;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.key.CellBuildingKey;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.res.BuildingCellConfig;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.api.java.function.Function;
import org.apache.spark.api.java.function.Function2;
import org.apache.spark.api.java.function.PairFunction;
import org.apache.spark.broadcast.Broadcast;
import scala.Serializable;
import scala.Tuple2;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

public class BuildingCellConfigUtils implements Serializable {

    private BuildingCellConfigUtils() {
        throw new IllegalStateException("Utility class");
    }

    public static List<BuildingCellConfig> dealOneCell(Iterable<CellAndBuilding> cellAndBuildings,
                                                       boolean isInTop90Percentile,
                                                       Set<BuildingKey> indoorCoverageBuildingIdSet,
                                                       Set<CellBuildingKey> lastMonthAlternativeAuxiliaryIndoorCellSet) {
        List<CellAndBuilding> cellAndBuildingList = StreamSupport
                .stream(cellAndBuildings.spliterator(), false)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (cellAndBuildingList.isEmpty()) {
            return Collections.emptyList();
        }

        Long cellTotalMrCnt = cellAndBuildingList.get(0).getCellTotalMrCnt();
        if (cellTotalMrCnt == null || cellTotalMrCnt <= 0) {
            return Collections.emptyList();
        }

        List<BuildingCellConfig> buildingCellConfigList = new ArrayList<>();
        // 采样点排序在90%以后的全部设置为 OTHER_CELL_A
        if (!isInTop90Percentile) {
            buildingCellConfigList.add(new BuildingCellConfig(cellAndBuildingList, CellRoleEnum.OTHER_CELL_A));
            return buildingCellConfigList;
        }

        // 采样点mrCnt小于300的全部设置为 OTHER_CELL_B
        if (cellTotalMrCnt <= 300) {
            buildingCellConfigList.add(new BuildingCellConfig(cellAndBuildingList, CellRoleEnum.OTHER_CELL_B));
            return buildingCellConfigList;
        }

        if (handleOtherCellC(cellAndBuildingList, cellTotalMrCnt, buildingCellConfigList)) {
            return buildingCellConfigList;
        }

        handleOtherCellD(cellAndBuildingList, cellTotalMrCnt, buildingCellConfigList);

        handleOtherCellE(cellAndBuildingList, cellTotalMrCnt, buildingCellConfigList);

        handleOtherCellF(cellAndBuildingList, cellTotalMrCnt, buildingCellConfigList);

        if (cellAndBuildingList.isEmpty()) {
            return buildingCellConfigList;
        }
        buildingCellConfigList.addAll(handleNormalRes(cellAndBuildingList,
                lastMonthAlternativeAuxiliaryIndoorCellSet, indoorCoverageBuildingIdSet));

        return buildingCellConfigList;
    }

    // 落到楼宇的采样点占小区总采样点比例小于90%的全部设置为 OTHER_CELL_C
    private static boolean handleOtherCellC(List<CellAndBuilding> cellAndBuildingList, Long cellTotalMrCnt, List<BuildingCellConfig> buildingCellConfigList) {
        long mrCntInBuilding = cellAndBuildingList.stream()
                .mapToLong(CellAndBuilding::getMrCnt)
                .sum();

        if ((double) mrCntInBuilding / cellTotalMrCnt < 0.9) {
            buildingCellConfigList.addAll(cellAndBuildingList.stream()
                    .map(cellAndBuilding -> new BuildingCellConfig(cellAndBuilding, CellRoleEnum.OTHER_CELL_C))
                    .collect(Collectors.toList()));
            return true;
        }
        return false;
    }

    // 落到楼宇内采样点占小区总采样点比例小于1%的楼宇设置为 OTHER_CELL_D
    private static void handleOtherCellD(List<CellAndBuilding> cellAndBuildingList, Long cellTotalMrCnt, List<BuildingCellConfig> buildingCellConfigList) {
        cellAndBuildingList.removeIf(cellAndBuilding -> {
            if ((double) cellAndBuilding.getMrCnt() / cellTotalMrCnt < 0.01) {
                buildingCellConfigList.add(new BuildingCellConfig(cellAndBuilding, CellRoleEnum.OTHER_CELL_D));
                return true;
            }
            return false;
        });
    }

    // 落在楼宇内采样点占小区总采样点比例小于平均值mrCnt的楼宇设置为 OTHER_CELL_E
    private static void handleOtherCellE(List<CellAndBuilding> cellAndBuildingList, Long cellTotalMrCnt, List<BuildingCellConfig> buildingCellConfigList) {
        if (cellAndBuildingList == null || cellTotalMrCnt == null || cellTotalMrCnt <= 0) {
            throw new IllegalArgumentException("Invalid input parameters: cellAndBuildingList or cellTotalMrCnt");
        }

        double avgMrCntRatio = cellAndBuildingList
                .stream()
                .filter(cellAndBuilding -> cellAndBuilding.getMrCnt() != null)
                .mapToDouble(cellAndBuilding -> (double) cellAndBuilding.getMrCnt() / cellTotalMrCnt)
                .average()
                .orElse(0D);
        BigDecimal avgMrCntRatioBD = BigDecimal
                .valueOf(avgMrCntRatio)
                .setScale(5, RoundingMode.HALF_UP);

        cellAndBuildingList.removeIf(cellAndBuilding -> {
            BigDecimal mrCntRatio = BigDecimal
                    .valueOf((double) cellAndBuilding.getMrCnt() / cellTotalMrCnt)
                    .setScale(5, RoundingMode.HALF_UP);
            if (mrCntRatio.compareTo(avgMrCntRatioBD) < 0) {
                buildingCellConfigList.add(new BuildingCellConfig(cellAndBuilding, CellRoleEnum.OTHER_CELL_E));
                return true;
            }
            return false;
        });
    }

    // 按采样点倒序排序后, 按比例累加, 大于80%的楼宇全部设置为 OTHER_CELL_F
    private static void handleOtherCellF(List<CellAndBuilding> cellAndBuildingList, Long cellTotalMrCnt, List<BuildingCellConfig> buildingCellConfigList) {
        BigDecimal ratioSum = BigDecimal.ZERO;
        cellAndBuildingList.sort((o1, o2) -> Long.compare(o2.getMrCnt(), o1.getMrCnt()));

        // 逻辑变量：标记是否已经找到临界点（累计比例 ≥0.8）
        boolean thresholdReached = false;

        Iterator<CellAndBuilding> iterator = cellAndBuildingList.iterator();
        while (iterator.hasNext()) {
            CellAndBuilding cellAndBuilding = iterator.next();

            if (!thresholdReached) {
                // 1. 未达到阈值时：计算累计比例
                BigDecimal buildingMrCntRatio = BigDecimal.valueOf(cellAndBuilding.getMrCnt())
                        .divide(BigDecimal.valueOf(cellTotalMrCnt), 6, RoundingMode.HALF_UP);
                ratioSum = ratioSum.add(buildingMrCntRatio);
                // 2. 检查是否首次达到（累计 >=0.8 且前一步未达标）
                if (ratioSum.compareTo(BigDecimal.valueOf(0.8)) >= 0) {
                    thresholdReached = true; // 触发标记
                }
                // 继续循环，不处理当前元素（它属于前k个）
            } else {
                // 3. 达到阈值后：直接标记后续所有元素为 OTHER_CELL_F
                buildingCellConfigList.add(
                        new BuildingCellConfig(cellAndBuilding, CellRoleEnum.OTHER_CELL_F)
                );
                iterator.remove(); // 从原列表移除已标记的元素
            }

        }
    }

    private static List<BuildingCellConfig> handleNormalRes(List<CellAndBuilding> cellAndBuildingList,
                                                            Set<CellBuildingKey> lastMonthAlternativeAuxiliaryIndoorCellSet,
                                                            Set<BuildingKey> indoorCoverageBuildingIdSet) {
        List<BuildingCellConfig> normalBuildingCellConfigResList = new ArrayList<>();

        long cellTotalMrCnt = cellAndBuildingList.get(0).getCellTotalMrCnt();
        long cellMaxMrCnt = cellAndBuildingList
                .stream()
                .mapToLong(CellAndBuilding::getMrCnt)
                .max()
                .orElse(0L);
        double cellMaxMrRatio = (double) cellMaxMrCnt / cellTotalMrCnt;

        for (CellAndBuilding cellAndBuilding : cellAndBuildingList) {
            boolean shouldBreak = false;

            // 小区采样点数等于最大值，生成主室分小区结果
            if (cellAndBuilding.getMrCnt() == cellMaxMrCnt) {
                BuildingCellConfig buildingCellConfig = new BuildingCellConfig(cellAndBuilding, CellRoleEnum.MAIN_INDOOR_CELL);
                normalBuildingCellConfigResList.add(buildingCellConfig);
                shouldBreak = true;
            }

            // 小区采样点数不是最大值,同时在室分楼宇配置表中，生成辅助室内分小区结果
            if (!shouldBreak && indoorCoverageBuildingIdSet.contains(new BuildingKey(cellAndBuilding))) {
                BuildingCellConfig buildingCellConfig = new BuildingCellConfig(cellAndBuilding, CellRoleEnum.AUXILIARY_INDOOR_CELL);
                normalBuildingCellConfigResList.add(buildingCellConfig);
                shouldBreak = true;
            }

            // 小区采样点数不是最大值,同时不在室分楼宇配置表中,但是前两期均为 保留次覆盖楼宇，生成辅助室内分小区结果
            if (!shouldBreak && lastMonthAlternativeAuxiliaryIndoorCellSet.contains(new CellBuildingKey(cellAndBuilding))) {
                BuildingCellConfig buildingCellConfig = new BuildingCellConfig(cellAndBuilding, CellRoleEnum.AUXILIARY_INDOOR_CELL);
                normalBuildingCellConfigResList.add(buildingCellConfig);
                shouldBreak = true;
            }

            // 小区采样点数不是最大值,同时不在室分楼宇配置表中,并且前两期不全为 保留次覆盖楼宇，生成保留辅助室内分小区结果
            if (!shouldBreak) {
                BuildingCellConfig buildingCellConfig = new BuildingCellConfig(cellAndBuilding, CellRoleEnum.ALTERNATIVE_AUXILIARY_INDOOR_CELL);
                normalBuildingCellConfigResList.add(buildingCellConfig);
            }
        }

        modifyNormalRes(normalBuildingCellConfigResList, cellMaxMrRatio);

        return normalBuildingCellConfigResList;
    }

    private static void modifyNormalRes(List<BuildingCellConfig> normalBuildingCellConfigResList, double cellMaxMrRatio) {
        // 小区对应的正常结果大于5条的,所有结果设置为 低置信度A
        if (normalBuildingCellConfigResList.size() > 5) {
            for (BuildingCellConfig buildingCellConfig : normalBuildingCellConfigResList) {
                buildingCellConfig.setCellRole(getLowConfidenceEnum(buildingCellConfig, "A"));
            }
            return;
        }

        // 主覆盖楼宇采样点占小区总采样点比例小于20%的,所有结果设置为 低置信度B
        if (cellMaxMrRatio < 0.2) {
            for (BuildingCellConfig buildingCellConfig : normalBuildingCellConfigResList) {
                buildingCellConfig.setCellRole(getLowConfidenceEnum(buildingCellConfig, "B"));
            }
        }
    }

    private static CellRoleEnum getLowConfidenceEnum(BuildingCellConfig buildingCellConfig, String type) {
        CellRoleEnum cellRole = buildingCellConfig.getCellRole();
        if (type.equalsIgnoreCase("A")) {
            if (CellRoleEnum.MAIN_INDOOR_CELL == cellRole) {
                return CellRoleEnum.LOW_CONFIDENCE_MAIN_INDOOR_CELL_A;
            } else if (CellRoleEnum.AUXILIARY_INDOOR_CELL == cellRole) {
                return CellRoleEnum.LOW_CONFIDENCE_AUXILIARY_INDOOR_CELL_A;
            } else if (CellRoleEnum.ALTERNATIVE_AUXILIARY_INDOOR_CELL == cellRole) {
                return CellRoleEnum.LOW_CONFIDENCE_ALTERNATIVE_AUXILIARY_INDOOR_CELL_A;
            }
        } else if (type.equalsIgnoreCase("B")) {
            if (CellRoleEnum.MAIN_INDOOR_CELL == cellRole) {
                return CellRoleEnum.LOW_CONFIDENCE_MAIN_INDOOR_CELL_B;
            } else if (CellRoleEnum.AUXILIARY_INDOOR_CELL == cellRole) {
                return CellRoleEnum.LOW_CONFIDENCE_AUXILIARY_INDOOR_CELL_B;
            } else if (CellRoleEnum.ALTERNATIVE_AUXILIARY_INDOOR_CELL == cellRole) {
                return CellRoleEnum.LOW_CONFIDENCE_ALTERNATIVE_AUXILIARY_INDOOR_CELL_B;
            }
        }
        return cellRole;
    }

    public static Broadcast<List<CellBuildingKey>> getHistoryAlternativeAuxiliaryResBroadcast(
            JavaSparkContext jsc, ProvinceEnum workProvince, String taskMonth) {
        List<CellBuildingKey> historyAlternativeAuxiliaryIndoorCellList = new ArrayList<>();
        String lastMonthResPath = LtjtPathUtils.getOutputPath("tb_building_cell_config",
                DateUtils.getMinusNMonthStr(taskMonth, 1), workProvince);
        String beforeLastMonthResPath = LtjtPathUtils.getOutputPath("tb_building_cell_config",
                DateUtils.getMinusNMonthStr(taskMonth, 2), workProvince);

        JavaRDD<String> lastMonthDataRDD = SourceUtils
                .read(jsc, lastMonthResPath, "last-month-tb_building_cell_config");
        JavaRDD<String> beforeLastMonthDataRDD = SourceUtils
                .read(jsc, beforeLastMonthResPath, "before-last-month-tb_building_cell_config");
        if (null == lastMonthDataRDD || null == beforeLastMonthDataRDD) {
            return jsc.broadcast(historyAlternativeAuxiliaryIndoorCellList);
        }
        historyAlternativeAuxiliaryIndoorCellList = lastMonthDataRDD
                .union(beforeLastMonthDataRDD)
                .map((Function<String, BuildingCellConfig>) BuildingCellConfig::new)
                .filter((Function<BuildingCellConfig, Boolean>) BuildingCellConfig::isAlternativeAuxiliaryIndoorCell)
                .mapToPair((PairFunction<BuildingCellConfig, CellBuildingKey, Integer>) buildingCellConfig ->
                        new Tuple2<>(new CellBuildingKey(buildingCellConfig), 1))
                .reduceByKey((Function2<Integer, Integer, Integer>) Integer::sum)
                .filter((Function<Tuple2<CellBuildingKey, Integer>, Boolean>) v1 ->
                        v1._2() >= 2)
                .keys()
                .collect();
        return jsc.broadcast(historyAlternativeAuxiliaryIndoorCellList);
    }
}
