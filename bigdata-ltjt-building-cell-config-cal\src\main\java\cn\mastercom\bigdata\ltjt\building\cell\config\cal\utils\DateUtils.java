package cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils;

import scala.Serializable;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

public class DateUtils implements Serializable {

    private DateUtils() {
        throw new IllegalStateException("Utility class");
    }

    public static LocalDate getDate() {
        return LocalDate.now();
    }

    public static LocalDate getDate(int year, int month, int day) {
        return LocalDate.of(year, month, day);
    }

    public static LocalDate getDateByString(String dateStr, String format) {
        if (dateStr == null || dateStr.isEmpty() || dateStr.equals("0"))
            return null;

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);

        // 解析字符串并转换为LocalDate对象
        return LocalDate.parse(dateStr, formatter);
    }

    public static String transDateToString(LocalDate date, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return date.format(formatter);
    }

    public static String transDateToString(LocalDate date) {
        return transDateToString(date, "yyyy-MM-dd");
    }

    public static LocalDate minusNDays(LocalDate date, int num) {
        return date.minusDays(num);
    }

    public static LocalDate plusNDays(LocalDate date, int num) {
        return date.plusDays(num);
    }

    public static List<LocalDate> getDateListOfMonth(String monthStr) {
        List<LocalDate> dateList = new ArrayList<>();
        LocalDate date = getDateByString(monthStr + "01", "yyMMdd");
        if (null == date) {
            return dateList;
        }

        int lengthOfMonth = date.lengthOfMonth();
        for (int i = 1; i <= lengthOfMonth; i++) {
            dateList.add(date.withDayOfMonth(i));
        }
        return dateList;
    }

    public static List<LocalDate> getCurrent30Days() {
        LocalDate date = getDate();
        List<LocalDate> dateList = new ArrayList<>();
        for (int i = 1; i <= 30; i++) {
            dateList.add(date.minusDays(i));
        }
        return dateList;
    }

    public static String getMinusNMonthStr(String monthStr, int n) {
        LocalDate date = getDateByString(monthStr + "01", "yyMMdd");
        if (null == date) {
            return "";
        }

        return transDateToString(date.minusMonths(n), "yyMM");
    }
}
