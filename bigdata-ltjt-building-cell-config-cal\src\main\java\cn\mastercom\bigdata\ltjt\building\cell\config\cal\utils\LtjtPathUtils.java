package cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils;

import cn.mastercom.bigdata.ltjt.building.cell.config.cal.conf.JobInfo;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.ProvinceEnum;
import cn.mastercom.mtcommon.spark.HdfsUtils;
import scala.Serializable;

import java.time.LocalDate;
import java.util.StringJoiner;

public class LtjtPathUtils implements Serializable {
    private static final String PATH_DELIMITER = "/";
    private static final String HDFS_PATH_PREFIX = "hdfs:" + PATH_DELIMITER + PATH_DELIMITER + "hdfsunity" + PATH_DELIMITER;

    private LtjtPathUtils() {
        throw new IllegalStateException("Utility class");
    }

    private static final String OUTPUT_ROOT_PATH = HDFS_PATH_PREFIX + "data" + PATH_DELIMITER +
            "wndoidAdmin_yuhui" + PATH_DELIMITER + "hive" + PATH_DELIMITER + "warehouse" + PATH_DELIMITER +
            "ads_wndoidadmin_yuhui.db" + PATH_DELIMITER + "config" + PATH_DELIMITER;

    private static final String OUTPUT_DICTIONARY_NAME = "/building-cell-config/";

    public static String getMdtDataPath(ProvinceEnum workProvince, LocalDate taskDate) {
        String rootPath = HDFS_PATH_PREFIX +
                "outputData" + PATH_DELIMITER + "yw" + PATH_DELIMITER + "mt_wzfw_mme2mr" + PATH_DELIMITER;
        return rootPath + "/" + workProvince.getAbbreviationsName() + "/SparkIdentify/mromdt/" +
                DateUtils.transDateToString(taskDate, JobInfo.YYYY_MM_DD) + "/*/mdt";
    }

    public static String getBuildingLayerSql(ProvinceEnum workProvince) {
        return "SELECT city_id, building_id, building_name, center_longitude, center_latitude, pointshape\n" +
                "FROM ads_wndoidadmin_yuhui.ads_zhw_wxcps_mtbd_building_pointshape_nd_last\n" +
                "WHERE province_id = " + workProvince.getLtjtId() + "";
    }

    public static String getIndoorCellBuildingConfigPath() {
        return HDFS_PATH_PREFIX + "data" + PATH_DELIMITER +
                "wndoidAdmin_yuhui" + PATH_DELIMITER + "hive" + PATH_DELIMITER + "warehouse" + PATH_DELIMITER +
                "ads_wndoidadmin_yuhui.db" + PATH_DELIMITER + "building-inner";
    }

    public static String getIndoorCellSql(ProvinceEnum workProvince) {
        return "WITH indoor_cell AS (SELECT city_id, eci, longitude, latitude\n" +
                "                     FROM ads_wndoidadmin_yuhui.ads_zhw_wxcps_mtbd_cell_lte_d_last\n" +
                "                     WHERE pt_province_id = " + workProvince.getLtjtId() + "\n" +
                "                       AND cover_type = '室内'\n" +
                "                     UNION\n" +
                "                     SELECT city_id, eci, longitude, latitude\n" +
                "                     FROM ads_wndoidadmin_yuhui.ads_zhw_wxcps_mtbd_cell_lte_dx_d_last\n" +
                "                     WHERE pt_province_id = " + workProvince.getLtjtId() + "\n" +
                "                       AND cover_type = '室内'\n" +
                "                       AND operator_name = '联通'),\n" +
                "     subway_cell AS (SELECT eci\n" +
                "                     FROM tmp_wndoidadmin_yuhui.tmp_subway_subscene_cell_lte\n" +
                "                     WHERE province_id = " + workProvince.getLtjtId() + ")\n" +
                "SELECT indoor_cell.city_id, indoor_cell.eci, indoor_cell.longitude, indoor_cell.latitude\n" +
                "FROM indoor_cell\n" +
                "WHERE NOT EXISTS (\n" +
                "    SELECT 1\n" +
                "    FROM subway_cell\n" +
                "    WHERE subway_cell.eci = indoor_cell.eci\n" +
                ")";
    }

    public static String getOutputPath(String dataName, LocalDate taskDate, ProvinceEnum provinceEnum) {
        String taskMonth = DateUtils.transDateToString(taskDate, "yyMM");
        return OUTPUT_ROOT_PATH + provinceEnum.getQuanPinName() + OUTPUT_DICTIONARY_NAME +
                taskMonth + PATH_DELIMITER + dataName + PATH_DELIMITER + DateUtils.transDateToString(taskDate, JobInfo.YYYY_MM_DD);
    }

    public static String getOutputPath(String dataName, String taskMonth, ProvinceEnum provinceEnum) {
        if (dataName.equalsIgnoreCase("tb_building_cell_config")) {
            return OUTPUT_ROOT_PATH + provinceEnum.getQuanPinName() + OUTPUT_DICTIONARY_NAME +
                    taskMonth + PATH_DELIMITER + dataName;
        } else {
            StringJoiner stringJoiner = new StringJoiner(",");
            for (LocalDate date : DateUtils.getDateListOfMonth(taskMonth)) {
                String path = OUTPUT_ROOT_PATH + provinceEnum.getQuanPinName() + OUTPUT_DICTIONARY_NAME +
                        taskMonth + PATH_DELIMITER + dataName + PATH_DELIMITER + DateUtils.transDateToString(date, JobInfo.YYYY_MM_DD);
                try {
                    if (HdfsUtils.pathExist(path)) {
                        stringJoiner.add(path);
                    }
                } catch (Exception ignored) {
                    // do nothing
                }
            }
            return stringJoiner.toString();
        }
    }
}