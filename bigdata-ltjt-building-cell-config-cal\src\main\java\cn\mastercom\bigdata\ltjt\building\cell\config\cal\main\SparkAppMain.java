package cn.mastercom.bigdata.ltjt.building.cell.config.cal.main;

import cn.mastercom.bigdata.ltjt.building.cell.config.cal.conf.JobInfo;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.TaskDataTypeEnum;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils.DateUtils;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.sql.SparkSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SparkAppMain {
    private static final Logger logger = LoggerFactory.getLogger(SparkAppMain.class);

    public static void main(String[] args) {
        if (args.length != 3) {
            logger.error("=== 输入参数个数错误！应该为3个参数：作业类型，计算日期，计算省份的三位ID ===");
            return;
        }
        JobInfo jobInfo = new JobInfo();
        if (!jobInfo.init(args)) {
            logger.error("=== 输入参数个数错误！应该为3个参数：作业类型，计算日期，计算省份的三位ID ===");
            return;
        }

        TaskDataTypeEnum taskDataTypeEnum = jobInfo.getTaskDataType();
        String appNameSuffix = (TaskDataTypeEnum.DAY == taskDataTypeEnum) ?
                taskDataTypeEnum + "-" + DateUtils.transDateToString(jobInfo.getTaskDate(), JobInfo.YYYY_MM_DD) :
                taskDataTypeEnum + "-" + jobInfo.getTaskMonth();
        logger.info("=== 开始进行楼宇-小区配置关系的计算，计算月份为{} ===", jobInfo.getTaskMonth());
        SparkSession sparkSession = SparkSession
                .builder()
                .enableHiveSupport()
                .master("yarn")
                .appName("LTJT-Building-Cell-Config-Cal-" + appNameSuffix)
                .getOrCreate();
        try (JavaSparkContext jsc = new JavaSparkContext(sparkSession.sparkContext());) {
            if (TaskDataTypeEnum.DAY.equals(taskDataTypeEnum)) {
                DayWorkMain dayWorkMain = new DayWorkMain(jobInfo);
                dayWorkMain.work(sparkSession, jsc);
            } else if (TaskDataTypeEnum.MONTH.equals(taskDataTypeEnum)) {
                MonthWorkMain monthWorkMain = new MonthWorkMain(jobInfo);
                monthWorkMain.work(sparkSession, jsc);
            } else {
                return;
            }
        }

        sparkSession.stop();
    }
}