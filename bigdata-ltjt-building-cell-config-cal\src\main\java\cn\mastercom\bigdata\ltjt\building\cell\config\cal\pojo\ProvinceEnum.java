package cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo;

public enum ProvinceEnum {
    BEIJING(110000, 101, "北京市", "北京",
            "beijing", "BJ"),
    SHANGHAI(310000, 102, "上海市", "上海",
            "shanghai", "SH"),
    GUANGDONG(440000, 103, "广东省", "广东",
            "guangdong", "GD"),
    CHONGQING(500000, 104, "重庆市", "重庆",
            "chongqing", "CQ"),
    HUNAN(430000, 105, "湖南省", "湖南",
            "hunan", "HUN"),
    QINGHAI(630000, 106, "青海省", "青海",
            "qinghai", "QH"),
    ANHUI(340000, 107, "安徽省", "安徽",
            "anhui", "AH"),
    FUJIAN(350000, 108, "福建省", "福建",
            "fujian", "FJ"),
    GANSU(620000, 109, "甘肃省", "甘肃",
            "gansu", "GS"),
    GUANGXI(450000, 110, "广西壮族自治区", "广西",
            "guangxi", "GX"),
    HAINAN(460000, 111, "海南省", "海南",
            "hainan", "HN"),
    JIANGXI(360000, 112, "江西省", "江西",
            "jiangxi", "JX"),
    TIANJIN(120000, 113, "天津市", "天津",
            "tianjin", "TJ"),
    JILIN(220000, 114, "吉林省", "吉林",
            "jilin", "JL"),
    HEILONGJIANG(230000, 115, "黑龙江省", "黑龙江",
            "heilongjiang", "HLJ"),
    LIAONING(210000, 116, "辽宁省", "辽宁",
            "liaoning", "LN"),
    HUBEI(420000, 117, "湖北省", "湖北",
            "hubei", "HUB"),
    XINJIANG(650000, 118, "新疆维吾尔自治区", "新疆",
            "xinjiang", "XJ"),
    NINGXIA(640000, 119, "宁夏回族自治区", "宁夏",
            "ningxia", "NX"),
    YUNNAN(530000, 120, "云南省", "云南",
            "yunnan", "YN"),
    ZHEJIANG(330000, 121, "浙江省", "浙江",
            "zhejiang", "ZJ"),
    SHANDONG(370000, 122, "山东省", "山东",
            "shandong", "SD"),
    XIZANG(540000, 123, "西藏自治区", "西藏",
            "xizang", "XZ"),
    NEIMENG(150000, 124, "内蒙古自治区", "内蒙古",
            "neimeng", "NM"),
    HENAN(410000, 125, "河南省", "河南",
            "henan", "HEN"),
    SICHUAN(510000, 126, "四川省", "四川",
            "sichuan", "SC"),
    HEBEI(130000, 127, "河北省", "河北",
            "hebei", "HEB"),
    SHANXI(140000, 128, "山西省", "山西",
            "shanxi", "SXI"),
    SHAANXI(610000, 129, "陕西省", "陕西",
            "shaanxi", "SX"),
    JIANGSU(320000, 130, "江苏省", "江西",
            "jiangsu", "JS"),
    GUIZHOU(520000, 131, "贵州省", "贵州",
            "guizhou", "GZ"),
    OTHER(-1, 100, "错误的", "错误",
            "error", "ERROR");

    private final Integer standardId;
    private final Integer ltjtId;
    private final String chineseName;
    private final String chineseAbbreviationName;
    private final String quanPinName;
    private final String abbreviationsName;

    ProvinceEnum(Integer standardId, Integer ltjtId, String chineseName, String chineseAbbreviationName,
                 String quanPinName, String abbreviationName) {
        this.standardId = standardId;
        this.ltjtId = ltjtId;
        this.chineseName = chineseName;
        this.chineseAbbreviationName = chineseAbbreviationName;
        this.quanPinName = quanPinName;
        this.abbreviationsName = abbreviationName;
    }

    public Integer getStandardId() {
        return standardId;
    }

    public Integer getLtjtId() {
        return ltjtId;
    }

    public String getChineseName() {
        return chineseName;
    }

    public String getChineseAbbreviationName() {
        return chineseAbbreviationName;
    }

    public String getQuanPinName() {
        return quanPinName;
    }

    public String getAbbreviationsName() {
        return abbreviationsName;
    }
}