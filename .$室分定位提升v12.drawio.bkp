<mxfile host="Electron" modified="2025-03-27T10:18:20.082Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.2.5 Chrome/120.0.6099.109 Electron/28.1.0 Safari/537.36" etag="AYRvvjMuweeyjf4Cq3My" version="24.2.5" type="device" pages="6">
  <diagram name="第 1 页" id="Gl8U5HqfLu4_IYvCTifV">
    <mxGraphModel dx="2074" dy="1197" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="QHrWRbxfoJhmZiQTFwwY-2" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="MEJKfUfqNdN42AETWuXO-1" target="QHrWRbxfoJhmZiQTFwwY-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-1" value="提取室分小区MDT月粒度采样点数据" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="409" y="460" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-2" value="室分楼宇配置表" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="409" y="120" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-3" value="楼宇图层位置" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="409" y="220" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-4" value="有室分楼宇" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="279" y="320" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-6" value="无室分楼宇" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="529" y="320" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-8" value="有室分楼宇&lt;br&gt;有室分MDT采样点" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="189" y="590" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-10" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-4" target="MEJKfUfqNdN42AETWuXO-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="459" y="630" as="sourcePoint" />
            <mxPoint x="509" y="580" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-11" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-6" target="MEJKfUfqNdN42AETWuXO-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="459" y="630" as="sourcePoint" />
            <mxPoint x="509" y="580" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-13" value="无室分楼宇&lt;br style=&quot;border-color: var(--border-color);&quot;&gt;有室分MDT采样点" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="499" y="590" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-14" value="无室分楼宇&lt;br style=&quot;border-color: var(--border-color);&quot;&gt;无室分MDT采样点" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="659" y="590" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-15" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-1" target="MEJKfUfqNdN42AETWuXO-8" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="459" y="710" as="sourcePoint" />
            <mxPoint x="509" y="660" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-17" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" target="MEJKfUfqNdN42AETWuXO-13" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="469" y="520" as="sourcePoint" />
            <mxPoint x="509" y="660" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-18" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-1" target="MEJKfUfqNdN42AETWuXO-14" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="459" y="710" as="sourcePoint" />
            <mxPoint x="509" y="660" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-20" value="不考虑" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="659" y="680" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-22" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-14" target="MEJKfUfqNdN42AETWuXO-20" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="459" y="610" as="sourcePoint" />
            <mxPoint x="509" y="560" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-24" value="楼宇内室分小区MDT采样点数统计" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="189" y="820" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-27" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-24" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="459" y="960" as="sourcePoint" />
            <mxPoint x="249" y="940" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-29" value="楼宇室分小区采样点数是否大于平均值" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="189" y="1050" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-30" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="249" y="1000" as="sourcePoint" />
            <mxPoint x="249" y="1050" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-32" value="剔除楼宇采样点TA大于2的采样点" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="189" y="710" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-33" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-8" target="MEJKfUfqNdN42AETWuXO-32" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="459" y="930" as="sourcePoint" />
            <mxPoint x="509" y="880" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-34" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-32" target="MEJKfUfqNdN42AETWuXO-24" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="459" y="910" as="sourcePoint" />
            <mxPoint x="509" y="860" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-36" value="取楼宇内室分小区采样点数&lt;font color=&quot;#ff0000&quot;&gt;最大值&lt;/font&gt;作为本期楼宇主室分小区" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="110" y="1160" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-37" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-29" target="MEJKfUfqNdN42AETWuXO-36" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="459" y="1300" as="sourcePoint" />
            <mxPoint x="509" y="1250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-38" value="楼宇内室分小区MDT采样点数统计" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="509" y="820" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-40" value="楼宇内室分小区MDT采样点数统计" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="509" y="920" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-41" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-38" target="MEJKfUfqNdN42AETWuXO-40" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="779" y="960" as="sourcePoint" />
            <mxPoint x="829" y="910" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-44" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="88OCIqy8_4XKkSVlLQuI-2" target="SlegZ9OdaJBCwldhEJD1-15" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="779" y="1140" as="sourcePoint" />
            <mxPoint x="569" y="1050" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-45" value="剔除楼宇采样点TA大于2的采样点" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="509" y="710" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-46" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-45" target="MEJKfUfqNdN42AETWuXO-38" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="779" y="910" as="sourcePoint" />
            <mxPoint x="829" y="860" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-50" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="559" y="650" as="sourcePoint" />
            <mxPoint x="559" y="710" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-51" value="界面显示样式：&lt;br&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="34" y="1620" width="760" height="40" as="geometry" />
        </mxCell>
        <mxCell id="MEJKfUfqNdN42AETWuXO-52" value="用户修改策略：" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="34" y="1731" width="760" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-2" value="室分小区" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="329" y="1901" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-3" value="小区覆盖楼宇数统计" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="329" y="2001" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-4" value="1个" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="169" y="2091" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-5" value="多个" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="319" y="2091" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-6" value="所有采样点均落在对应楼宇内" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="169" y="2211" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-7" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-4" target="dTuDczm5kZ5u2p71eDUZ-6" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="429" y="2261" as="sourcePoint" />
            <mxPoint x="479" y="2211" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-8" value="有对应关系楼宇采样点" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="189" y="2321" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-9" value="无对应关系楼宇的采样点" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="319" y="2321" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-12" value="吸附周边50米内采样点，吸附原则：距离哪个楼宇边框近属于哪个楼宇" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="189" y="2421" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-13" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-8" target="dTuDczm5kZ5u2p71eDUZ-12" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="429" y="2351" as="sourcePoint" />
            <mxPoint x="479" y="2301" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-14" value="采样点与楼宇进行关联匹配" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="319" y="2211" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-15" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-5" target="dTuDczm5kZ5u2p71eDUZ-14" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="429" y="2231" as="sourcePoint" />
            <mxPoint x="479" y="2181" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-16" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-14" target="dTuDczm5kZ5u2p71eDUZ-8" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="429" y="2231" as="sourcePoint" />
            <mxPoint x="479" y="2181" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-17" value="其他未落在楼宇图层上的采样点" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="449" y="2321" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-18" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" target="dTuDczm5kZ5u2p71eDUZ-9" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="379" y="2271" as="sourcePoint" />
            <mxPoint x="479" y="2181" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-19" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-14" target="dTuDczm5kZ5u2p71eDUZ-17" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="429" y="2231" as="sourcePoint" />
            <mxPoint x="479" y="2181" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-21" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-9" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="429" y="2221" as="sourcePoint" />
            <mxPoint x="429" y="2421" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-22" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-17" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="429" y="2221" as="sourcePoint" />
            <mxPoint x="429" y="2421" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-24" value="有关楼宇指标的统计，这些采样点丢弃，不计算到楼宇指标内。" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="369" y="2421" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-27" value="无" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="489" y="2091" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-29" value="先按照LGB进行落位置" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="488" y="2171" width="121" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-30" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-27" target="dTuDczm5kZ5u2p71eDUZ-29" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="429" y="2311" as="sourcePoint" />
            <mxPoint x="479" y="2261" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-31" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-2" target="dTuDczm5kZ5u2p71eDUZ-3" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="429" y="2011" as="sourcePoint" />
            <mxPoint x="479" y="1961" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-32" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.45;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-3" target="dTuDczm5kZ5u2p71eDUZ-5" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="429" y="2011" as="sourcePoint" />
            <mxPoint x="479" y="1961" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-33" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-3" target="dTuDczm5kZ5u2p71eDUZ-4" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="429" y="2011" as="sourcePoint" />
            <mxPoint x="479" y="1961" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dTuDczm5kZ5u2p71eDUZ-34" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-3" target="dTuDczm5kZ5u2p71eDUZ-27" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="429" y="2011" as="sourcePoint" />
            <mxPoint x="479" y="1961" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="SlegZ9OdaJBCwldhEJD1-6" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-3" target="MEJKfUfqNdN42AETWuXO-4" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="319" y="560" as="sourcePoint" />
            <mxPoint x="369" y="510" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="SlegZ9OdaJBCwldhEJD1-7" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" target="MEJKfUfqNdN42AETWuXO-6" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="469" y="280" as="sourcePoint" />
            <mxPoint x="349" y="330" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="SlegZ9OdaJBCwldhEJD1-8" value="楼宇与室分小区对应算法：&lt;br&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="40" y="20" width="758" height="50" as="geometry" />
        </mxCell>
        <mxCell id="SlegZ9OdaJBCwldhEJD1-9" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-2" target="MEJKfUfqNdN42AETWuXO-3" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="510" y="290" as="sourcePoint" />
            <mxPoint x="590" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="SlegZ9OdaJBCwldhEJD1-11" value="&lt;span style=&quot;color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;&quot;&gt;楼宇主室分小区：楼宇内室分小区采样频次占比最大值对应的小区&lt;/span&gt;&lt;br style=&quot;border-color: var(--border-color); color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;&quot;&gt;&lt;div style=&quot;border-color: var(--border-color); color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; text-align: left;&quot;&gt;&lt;span style=&quot;border-color: var(--border-color); background-color: initial;&quot;&gt;楼宇辅室分小区：辅室分小区&lt;br&gt;注：用户可以自己进行修改。&lt;/span&gt;&lt;/div&gt;" style="text;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="69" y="1670" width="700" height="50" as="geometry" />
        </mxCell>
        <mxCell id="SlegZ9OdaJBCwldhEJD1-12" value="&lt;span style=&quot;color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;&quot;&gt;1、允许特定用户对楼宇对应室分关系进行增加、删除操作。&lt;br&gt;&lt;/span&gt;2、&lt;span style=&quot;color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;&quot;&gt;对用户增加的小区，删除的小区，形成一张用户操作记录表，表内有楼宇ID、小区主次，小区ECI、增加/删除、时间&lt;/span&gt;" style="text;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="79.5" y="1791" width="651" height="40" as="geometry" />
        </mxCell>
        <mxCell id="SlegZ9OdaJBCwldhEJD1-13" value="取楼宇内室分小区采样点数&lt;font color=&quot;#ff3333&quot;&gt;大于平均值小于最大值&lt;/font&gt;作为楼宇本期辅室分小区" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="250" y="1160" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="SlegZ9OdaJBCwldhEJD1-14" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-29" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="430" y="1330" as="sourcePoint" />
            <mxPoint x="320" y="1160" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="SlegZ9OdaJBCwldhEJD1-15" value="取楼宇内室分小区采样点占比&lt;font color=&quot;#ff0000&quot;&gt;最大值&lt;/font&gt;作为&lt;font color=&quot;#3399ff&quot;&gt;本期&lt;/font&gt;楼宇主室分小区" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="430" y="1110" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="SlegZ9OdaJBCwldhEJD1-16" value="有楼宇与室分小区对应关系后的采样点与楼宇匹配策略" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="34" y="1831" width="760" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Twr2VkyiY78_Iyu74IA3-1" value="小区对应多个楼宇时，" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="359" y="1270" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Twr2VkyiY78_Iyu74IA3-2" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="MEJKfUfqNdN42AETWuXO-36" target="Twr2VkyiY78_Iyu74IA3-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="1450" as="sourcePoint" />
            <mxPoint x="440" y="1400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Twr2VkyiY78_Iyu74IA3-3" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="SlegZ9OdaJBCwldhEJD1-13" target="Twr2VkyiY78_Iyu74IA3-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="1450" as="sourcePoint" />
            <mxPoint x="440" y="1400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Twr2VkyiY78_Iyu74IA3-6" value="采样点占比小剔除：剔除落到楼宇内小区采样点数占小区总采样点比例少于20%的楼宇" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="334" y="1370" width="170" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Twr2VkyiY78_Iyu74IA3-7" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="Twr2VkyiY78_Iyu74IA3-1" target="Twr2VkyiY78_Iyu74IA3-6" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="1240" as="sourcePoint" />
            <mxPoint x="440" y="1190" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Twr2VkyiY78_Iyu74IA3-8" value="所有采样点均落在室分小区位置" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="489" y="2251" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Twr2VkyiY78_Iyu74IA3-12" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="dTuDczm5kZ5u2p71eDUZ-29" target="Twr2VkyiY78_Iyu74IA3-8" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="420" y="2241" as="sourcePoint" />
            <mxPoint x="470" y="2191" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="v47DIhSJnMT9DxbWAAaH-1" value="取楼宇内室分小区采样点占比&lt;font style=&quot;border-color: var(--border-color);&quot; color=&quot;#ff3333&quot;&gt;大于平均值小于最大值&lt;/font&gt;作为楼宇&lt;font color=&quot;#3399ff&quot;&gt;保留&lt;/font&gt;辅室分小区" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="590" y="1100" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="v47DIhSJnMT9DxbWAAaH-2" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="88OCIqy8_4XKkSVlLQuI-2" target="v47DIhSJnMT9DxbWAAaH-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="570" y="1000" as="sourcePoint" />
            <mxPoint x="539" y="1060" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="v47DIhSJnMT9DxbWAAaH-3" value="&lt;font color=&quot;#ff6666&quot;&gt;下期如果保留的小区楼宇室分小区采样点占比大于平均值则作为下一起楼宇辅室分小区&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontColor=#3399FF;" parent="1" vertex="1">
          <mxGeometry x="590" y="1180" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="v47DIhSJnMT9DxbWAAaH-4" value="" style="endArrow=classic;html=1;rounded=0;fontColor=#FF6666;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="v47DIhSJnMT9DxbWAAaH-1" target="v47DIhSJnMT9DxbWAAaH-3" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="1110" as="sourcePoint" />
            <mxPoint x="440" y="1060" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="v47DIhSJnMT9DxbWAAaH-5" value="" style="endArrow=classic;html=1;rounded=0;fontColor=#FF6666;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="SlegZ9OdaJBCwldhEJD1-15" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="1110" as="sourcePoint" />
            <mxPoint x="420" y="1270" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="v47DIhSJnMT9DxbWAAaH-6" value="" style="endArrow=classic;html=1;rounded=0;fontColor=#FF6666;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="v47DIhSJnMT9DxbWAAaH-3" target="Twr2VkyiY78_Iyu74IA3-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="1110" as="sourcePoint" />
            <mxPoint x="440" y="1060" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="BKGFdhFcGff8IqCUX1Js-1" value="楼间距异常剔除：剔除小区覆盖下属楼宇，相互间距离最小值大于500米的楼宇" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="339" y="1470" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="BKGFdhFcGff8IqCUX1Js-2" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" target="BKGFdhFcGff8IqCUX1Js-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="419" y="1430" as="sourcePoint" />
            <mxPoint x="419.4000000000001" y="1429" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="88OCIqy8_4XKkSVlLQuI-1" value="楼宇内室分小区MDT采样点数统计" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="189" y="940" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="88OCIqy8_4XKkSVlLQuI-2" value="楼宇室分小区采样点数是否大于平均值" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="509" y="1020" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="88OCIqy8_4XKkSVlLQuI-7" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="568.43" y="980" as="sourcePoint" />
            <mxPoint x="568.43" y="1020" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="QHrWRbxfoJhmZiQTFwwY-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="QHrWRbxfoJhmZiQTFwwY-1" target="QHrWRbxfoJhmZiQTFwwY-3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="QHrWRbxfoJhmZiQTFwwY-1" value="无室分楼宇&lt;br style=&quot;border-color: var(--border-color);&quot;&gt;无室分MDT采样点" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="354" y="590" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="QHrWRbxfoJhmZiQTFwwY-3" value="不考虑" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="354" y="690" width="120" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="BHEEye47jg_9a50YjLHO" name="v1">
    <mxGraphModel dx="2074" dy="1197" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="albcE6jhP9YvvwfOzbzl-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="jLy5Qbelym_fzd4RW6AO-1" target="jLy5Qbelym_fzd4RW6AO-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="jLy5Qbelym_fzd4RW6AO-1" value="室分楼宇配置表" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="530" y="220" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="htk9kU5CBMiG8TAa8aaG-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="jLy5Qbelym_fzd4RW6AO-4" target="albcE6jhP9YvvwfOzbzl-4" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="590" y="1090" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="jLy5Qbelym_fzd4RW6AO-4" value="楼宇id+是否有室分覆盖配置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="530" y="420" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LqzpJ_ME0kbuA20bGvdl-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="jLy5Qbelym_fzd4RW6AO-8" target="LqzpJ_ME0kbuA20bGvdl-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="jLy5Qbelym_fzd4RW6AO-8" value="mdt采样点(整月)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="890" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="utr8etj-6jkVzBQ4fML0-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="jLy5Qbelym_fzd4RW6AO-9" target="utr8etj-6jkVzBQ4fML0-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="jLy5Qbelym_fzd4RW6AO-9" value="(参与图层关联的采样点)经纬度+eci" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="890" y="310" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="jLy5Qbelym_fzd4RW6AO-11" value="TA大于2的采样点" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="890" y="270" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="utr8etj-6jkVzBQ4fML0-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="jLy5Qbelym_fzd4RW6AO-16" target="utr8etj-6jkVzBQ4fML0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="755" y="390" />
              <mxPoint x="950" y="390" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="albcE6jhP9YvvwfOzbzl-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="jLy5Qbelym_fzd4RW6AO-16" target="jLy5Qbelym_fzd4RW6AO-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="jLy5Qbelym_fzd4RW6AO-16" value="楼宇图层" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="720" y="300" width="70" height="30" as="geometry" />
        </mxCell>
        <mxCell id="sXCLjzXY6jWjYLR0dM1H-78" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="utr8etj-6jkVzBQ4fML0-1" target="sXCLjzXY6jWjYLR0dM1H-76" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="utr8etj-6jkVzBQ4fML0-1" value="楼宇id+eci+采样点个数" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="890" y="420" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y44VkHj5x1n_a01yPVmy-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="sXCLjzXY6jWjYLR0dM1H-72" target="y44VkHj5x1n_a01yPVmy-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="sXCLjzXY6jWjYLR0dM1H-72" value="统计楼宇内不同室分小区采样点占比" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="890" y="655" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="sXCLjzXY6jWjYLR0dM1H-75" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="sXCLjzXY6jWjYLR0dM1H-76" target="sXCLjzXY6jWjYLR0dM1H-72" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="sXCLjzXY6jWjYLR0dM1H-76" value="按楼宇id进行汇聚" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="890" y="530" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MATF-sM5usYceBuwxZyg-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="sXCLjzXY6jWjYLR0dM1H-79" target="ZiUwMDU_Nl_DjrNQZuaP-21" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1050" y="1465" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="sXCLjzXY6jWjYLR0dM1H-79" value="楼宇本期&lt;font color=&quot;#ff0000&quot;&gt;主室分小区&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="990" y="1050" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MATF-sM5usYceBuwxZyg-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="sXCLjzXY6jWjYLR0dM1H-83" target="ZiUwMDU_Nl_DjrNQZuaP-21" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="930" y="1410" />
              <mxPoint x="860" y="1410" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="sXCLjzXY6jWjYLR0dM1H-83" value="楼宇本期辅室分小区" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="870" y="1185" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="albcE6jhP9YvvwfOzbzl-4" target="sXCLjzXY6jWjYLR0dM1H-83" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="840" y="1160" />
              <mxPoint x="930" y="1160" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-19" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZiUwMDU_Nl_DjrNQZuaP-17" vertex="1" connectable="0">
          <mxGeometry x="-0.0134" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="albcE6jhP9YvvwfOzbzl-4" target="MATF-sM5usYceBuwxZyg-6" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="713" y="1180" as="targetPoint" />
            <Array as="points">
              <mxPoint x="840" y="1160" />
              <mxPoint x="713" y="1160" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-20" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZiUwMDU_Nl_DjrNQZuaP-18" vertex="1" connectable="0">
          <mxGeometry x="0.0731" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="albcE6jhP9YvvwfOzbzl-4" value="是否有室分配置" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="765" y="1050" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-27" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ZiUwMDU_Nl_DjrNQZuaP-21" target="ZiUwMDU_Nl_DjrNQZuaP-26" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-21" value="楼宇id+eci+role(主室分小区，辅室分小区，保留辅室分小区)+采样点个数" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="757.5" y="1430" width="205" height="70" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-29" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ZiUwMDU_Nl_DjrNQZuaP-26" target="ZiUwMDU_Nl_DjrNQZuaP-28" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-26" value="按eci进行分组" style="whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="800" y="1550" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-34" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="ZiUwMDU_Nl_DjrNQZuaP-28" target="ZiUwMDU_Nl_DjrNQZuaP-42" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="945" y="1830" as="targetPoint" />
            <Array as="points">
              <mxPoint x="860" y="1780" />
              <mxPoint x="950" y="1780" />
              <mxPoint x="950" y="2130" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-35" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZiUwMDU_Nl_DjrNQZuaP-34" vertex="1" connectable="0">
          <mxGeometry x="0.0772" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-37" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ZiUwMDU_Nl_DjrNQZuaP-28" target="ZiUwMDU_Nl_DjrNQZuaP-36" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-38" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZiUwMDU_Nl_DjrNQZuaP-37" vertex="1" connectable="0">
          <mxGeometry x="0.2398" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-28" value="同一个eci是否有多条数据(&lt;div&gt;即小区是否对应多个楼宇)&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="715" y="1650" width="290" height="80" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-40" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ZiUwMDU_Nl_DjrNQZuaP-36" target="ZiUwMDU_Nl_DjrNQZuaP-39" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-41" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZiUwMDU_Nl_DjrNQZuaP-40" vertex="1" connectable="0">
          <mxGeometry x="-0.0714" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="eX6pLUD1fp5CWvsyR2SY-5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ZiUwMDU_Nl_DjrNQZuaP-36" target="eX6pLUD1fp5CWvsyR2SY-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eX6pLUD1fp5CWvsyR2SY-6" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="eX6pLUD1fp5CWvsyR2SY-5" vertex="1" connectable="0">
          <mxGeometry x="-0.0148" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-36" value="落到楼宇内小区采样点数占&lt;div&gt;小区总采样点比例是否大于等于20%&lt;br&gt;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="570" y="1830" width="290" height="80" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-43" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ZiUwMDU_Nl_DjrNQZuaP-39" target="ZiUwMDU_Nl_DjrNQZuaP-42" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-44" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZiUwMDU_Nl_DjrNQZuaP-43" vertex="1" connectable="0">
          <mxGeometry x="-0.1857" y="3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="eX6pLUD1fp5CWvsyR2SY-8" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ZiUwMDU_Nl_DjrNQZuaP-39" target="eX6pLUD1fp5CWvsyR2SY-7" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eX6pLUD1fp5CWvsyR2SY-9" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="eX6pLUD1fp5CWvsyR2SY-8" vertex="1" connectable="0">
          <mxGeometry x="-0.1199" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-39" value="同组内是否存在距离小于等于500米的楼宇" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="570" y="1950" width="290" height="80" as="geometry" />
        </mxCell>
        <mxCell id="ZiUwMDU_Nl_DjrNQZuaP-42" value="输出结果" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="655" y="2100" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MATF-sM5usYceBuwxZyg-8" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="MATF-sM5usYceBuwxZyg-6" target="MATF-sM5usYceBuwxZyg-7" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MATF-sM5usYceBuwxZyg-11" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="MATF-sM5usYceBuwxZyg-8" vertex="1" connectable="0">
          <mxGeometry x="0.1503" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MATF-sM5usYceBuwxZyg-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="MATF-sM5usYceBuwxZyg-6" target="MATF-sM5usYceBuwxZyg-9" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MATF-sM5usYceBuwxZyg-12" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="MATF-sM5usYceBuwxZyg-10" vertex="1" connectable="0">
          <mxGeometry x="0.0085" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MATF-sM5usYceBuwxZyg-6" value="上一期是否为&lt;div&gt;保留辅室分小区&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="640" y="1175" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="MATF-sM5usYceBuwxZyg-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="MATF-sM5usYceBuwxZyg-7" target="ZiUwMDU_Nl_DjrNQZuaP-21" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="640" y="1410" />
              <mxPoint x="860" y="1410" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="MATF-sM5usYceBuwxZyg-7" value="楼宇本期辅室分小区" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="580" y="1300" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MATF-sM5usYceBuwxZyg-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="MATF-sM5usYceBuwxZyg-9" target="ZiUwMDU_Nl_DjrNQZuaP-21" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="800" y="1410" />
              <mxPoint x="860" y="1410" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="MATF-sM5usYceBuwxZyg-9" value="楼宇本期&lt;font color=&quot;#3399ff&quot;&gt;保留&lt;/font&gt;辅室分小区" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="740" y="1300" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y44VkHj5x1n_a01yPVmy-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="y44VkHj5x1n_a01yPVmy-1" target="y44VkHj5x1n_a01yPVmy-3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y44VkHj5x1n_a01yPVmy-5" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="y44VkHj5x1n_a01yPVmy-4" vertex="1" connectable="0">
          <mxGeometry x="-0.0778" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="eX6pLUD1fp5CWvsyR2SY-2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="y44VkHj5x1n_a01yPVmy-1" target="eX6pLUD1fp5CWvsyR2SY-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eX6pLUD1fp5CWvsyR2SY-3" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="eX6pLUD1fp5CWvsyR2SY-2" vertex="1" connectable="0">
          <mxGeometry x="-0.0496" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="y44VkHj5x1n_a01yPVmy-1" value="小区采样点数是否&lt;div&gt;大于平均值&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="875" y="740" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="y44VkHj5x1n_a01yPVmy-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="y44VkHj5x1n_a01yPVmy-3" target="sXCLjzXY6jWjYLR0dM1H-79" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y44VkHj5x1n_a01yPVmy-8" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="y44VkHj5x1n_a01yPVmy-6" vertex="1" connectable="0">
          <mxGeometry x="0.0491" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="y44VkHj5x1n_a01yPVmy-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="y44VkHj5x1n_a01yPVmy-3" target="albcE6jhP9YvvwfOzbzl-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y44VkHj5x1n_a01yPVmy-11" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="y44VkHj5x1n_a01yPVmy-10" vertex="1" connectable="0">
          <mxGeometry x="0.0749" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="y44VkHj5x1n_a01yPVmy-3" value="小区采样点数是否&lt;div&gt;为该楼宇内所有小区&lt;/div&gt;&lt;div&gt;的最大值&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="865" y="860" width="170" height="100" as="geometry" />
        </mxCell>
        <mxCell id="LqzpJ_ME0kbuA20bGvdl-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="5uuiTCWoT44TRy1-VWHP-1" target="LqzpJ_ME0kbuA20bGvdl-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LqzpJ_ME0kbuA20bGvdl-4" value="&lt;span style=&quot;font-size: 12px; background-color: rgb(251, 251, 251);&quot;&gt;过滤非室分小区与经纬度异常&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="LqzpJ_ME0kbuA20bGvdl-3" vertex="1" connectable="0">
          <mxGeometry x="0.6562" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="5uuiTCWoT44TRy1-VWHP-1" value="工参" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1025" y="120" width="50" height="20" as="geometry" />
        </mxCell>
        <mxCell id="eX6pLUD1fp5CWvsyR2SY-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="eX6pLUD1fp5CWvsyR2SY-1" target="ZiUwMDU_Nl_DjrNQZuaP-21" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1315" y="1465" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="eX6pLUD1fp5CWvsyR2SY-1" value="楼宇本期其他小区" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="1270" y="760" width="90" height="40" as="geometry" />
        </mxCell>
        <mxCell id="eX6pLUD1fp5CWvsyR2SY-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="eX6pLUD1fp5CWvsyR2SY-4" target="ZiUwMDU_Nl_DjrNQZuaP-42" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="340" y="1870" />
              <mxPoint x="340" y="2130" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="eX6pLUD1fp5CWvsyR2SY-4" value="楼宇本期其他小区" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="380" y="1840" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="eX6pLUD1fp5CWvsyR2SY-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="eX6pLUD1fp5CWvsyR2SY-7" target="ZiUwMDU_Nl_DjrNQZuaP-42" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="440" y="2130" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="eX6pLUD1fp5CWvsyR2SY-7" value="楼宇本期其他小区" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="380" y="1960" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LqzpJ_ME0kbuA20bGvdl-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="LqzpJ_ME0kbuA20bGvdl-1" target="jLy5Qbelym_fzd4RW6AO-9" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LqzpJ_ME0kbuA20bGvdl-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="LqzpJ_ME0kbuA20bGvdl-1" target="LqzpJ_ME0kbuA20bGvdl-6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LqzpJ_ME0kbuA20bGvdl-1" value="有效采样点" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="890" y="190" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LqzpJ_ME0kbuA20bGvdl-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="LqzpJ_ME0kbuA20bGvdl-6" target="ZiUwMDU_Nl_DjrNQZuaP-28" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1480" y="1690" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="LqzpJ_ME0kbuA20bGvdl-6" value="eci-总采样点数" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1420" y="190" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LqzpJ_ME0kbuA20bGvdl-8" value="按小区分组汇聚" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="1160" y="198" width="110" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="1vKEaSeDo2MVEbxu2R_d" name="v2-不过滤ta值">
    <mxGraphModel dx="1418" dy="828" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-82" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-83" target="K5VGfCS9qDh2TPaLQJ5_-85" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-83" value="室分楼宇配置表" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="540" y="230" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-84" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-85" target="K5VGfCS9qDh2TPaLQJ5_-108" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="600" y="1100" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-85" value="楼宇id+是否有室分覆盖配置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="540" y="430" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-86" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-87" target="K5VGfCS9qDh2TPaLQJ5_-159" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-87" value="mdt采样点(整月)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="900" y="50" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-88" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-89" target="K5VGfCS9qDh2TPaLQJ5_-95" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-89" value="(参与图层关联的采样点)经纬度+eci" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="900" y="320" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-91" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-93" target="K5VGfCS9qDh2TPaLQJ5_-95" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="765" y="400" />
              <mxPoint x="960" y="400" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-92" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-93" target="K5VGfCS9qDh2TPaLQJ5_-85" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-93" value="楼宇图层" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="730" y="310" width="70" height="30" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-94" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-95" target="K5VGfCS9qDh2TPaLQJ5_-99" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-95" value="楼宇id+eci+采样点个数" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="900" y="430" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-96" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-97" target="K5VGfCS9qDh2TPaLQJ5_-142" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-97" value="统计楼宇内不同室分小区采样点占比" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="900" y="665" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-98" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-99" target="K5VGfCS9qDh2TPaLQJ5_-97" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-99" value="按楼宇id进行汇聚" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="900" y="540" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-100" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-101" target="K5VGfCS9qDh2TPaLQJ5_-110" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1060" y="1475" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-101" value="楼宇本期&lt;font color=&quot;#ff0000&quot;&gt;主室分小区&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="1000" y="1060" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-102" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-103" target="K5VGfCS9qDh2TPaLQJ5_-110" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="940" y="1420" />
              <mxPoint x="870" y="1420" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-103" value="楼宇本期辅室分小区" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="880" y="1195" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-104" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-108" target="K5VGfCS9qDh2TPaLQJ5_-103" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="850" y="1170" />
              <mxPoint x="940" y="1170" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-105" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="K5VGfCS9qDh2TPaLQJ5_-104" vertex="1" connectable="0">
          <mxGeometry x="-0.0134" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-106" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-108" target="K5VGfCS9qDh2TPaLQJ5_-133" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="723" y="1190" as="targetPoint" />
            <Array as="points">
              <mxPoint x="850" y="1170" />
              <mxPoint x="723" y="1170" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-107" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="K5VGfCS9qDh2TPaLQJ5_-106" vertex="1" connectable="0">
          <mxGeometry x="0.0731" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-108" value="是否有室分配置" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="775" y="1060" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-109" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-110" target="K5VGfCS9qDh2TPaLQJ5_-112" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-110" value="楼宇id+eci+role(主室分小区，辅室分小区，保留辅室分小区)+采样点个数" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="767.5" y="1440" width="205" height="70" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-111" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-112" target="K5VGfCS9qDh2TPaLQJ5_-117" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-112" value="按eci进行分组" style="whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="810" y="1560" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-113" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-117" target="K5VGfCS9qDh2TPaLQJ5_-128" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="955" y="1840" as="targetPoint" />
            <Array as="points">
              <mxPoint x="870" y="1790" />
              <mxPoint x="960" y="1790" />
              <mxPoint x="960" y="2140" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-114" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="K5VGfCS9qDh2TPaLQJ5_-113" vertex="1" connectable="0">
          <mxGeometry x="0.0772" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-115" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-117" target="K5VGfCS9qDh2TPaLQJ5_-122" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-116" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="K5VGfCS9qDh2TPaLQJ5_-115" vertex="1" connectable="0">
          <mxGeometry x="0.2398" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-117" value="同一个eci是否有多条数据(&lt;div&gt;即小区是否对应多个楼宇)&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="725" y="1660" width="290" height="80" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-118" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-122" target="K5VGfCS9qDh2TPaLQJ5_-127" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-119" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="K5VGfCS9qDh2TPaLQJ5_-118" vertex="1" connectable="0">
          <mxGeometry x="-0.0714" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-120" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-122" target="K5VGfCS9qDh2TPaLQJ5_-154" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-121" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="K5VGfCS9qDh2TPaLQJ5_-120" vertex="1" connectable="0">
          <mxGeometry x="-0.0148" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-122" value="落到楼宇内小区采样点数占&lt;div&gt;小区总采样点比例是否大于等于20%&lt;br&gt;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="580" y="1840" width="290" height="80" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-123" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-127" target="K5VGfCS9qDh2TPaLQJ5_-128" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-124" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="K5VGfCS9qDh2TPaLQJ5_-123" vertex="1" connectable="0">
          <mxGeometry x="-0.1857" y="3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-125" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-127" target="K5VGfCS9qDh2TPaLQJ5_-156" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-126" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="K5VGfCS9qDh2TPaLQJ5_-125" vertex="1" connectable="0">
          <mxGeometry x="-0.1199" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-127" value="同组内是否存在距离小于等于500米的楼宇" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="580" y="1960" width="290" height="80" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-128" value="输出结果" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="665" y="2110" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-129" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-133" target="K5VGfCS9qDh2TPaLQJ5_-135" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-130" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="K5VGfCS9qDh2TPaLQJ5_-129" vertex="1" connectable="0">
          <mxGeometry x="0.1503" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-131" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-133" target="K5VGfCS9qDh2TPaLQJ5_-137" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-132" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="K5VGfCS9qDh2TPaLQJ5_-131" vertex="1" connectable="0">
          <mxGeometry x="0.0085" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-133" value="上一期是否为&lt;div&gt;保留辅室分小区&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="650" y="1185" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-134" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-135" target="K5VGfCS9qDh2TPaLQJ5_-110" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="650" y="1420" />
              <mxPoint x="870" y="1420" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-135" value="楼宇本期辅室分小区" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="590" y="1310" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-136" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-137" target="K5VGfCS9qDh2TPaLQJ5_-110" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="810" y="1420" />
              <mxPoint x="870" y="1420" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-137" value="楼宇本期&lt;font color=&quot;#3399ff&quot;&gt;保留&lt;/font&gt;辅室分小区" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="750" y="1310" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-138" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-142" target="K5VGfCS9qDh2TPaLQJ5_-147" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-139" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="K5VGfCS9qDh2TPaLQJ5_-138" vertex="1" connectable="0">
          <mxGeometry x="-0.0778" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-140" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-142" target="K5VGfCS9qDh2TPaLQJ5_-152" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-141" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="K5VGfCS9qDh2TPaLQJ5_-140" vertex="1" connectable="0">
          <mxGeometry x="-0.0496" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-142" value="小区采样点数是否&lt;div&gt;大于平均值&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="885" y="750" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-143" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-147" target="K5VGfCS9qDh2TPaLQJ5_-101" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-144" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="K5VGfCS9qDh2TPaLQJ5_-143" vertex="1" connectable="0">
          <mxGeometry x="0.0491" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-145" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-147" target="K5VGfCS9qDh2TPaLQJ5_-108" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-146" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="K5VGfCS9qDh2TPaLQJ5_-145" vertex="1" connectable="0">
          <mxGeometry x="0.0749" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-147" value="小区采样点数是否&lt;div&gt;为该楼宇内所有小区&lt;/div&gt;&lt;div&gt;的最大值&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="875" y="870" width="170" height="100" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-148" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-150" target="K5VGfCS9qDh2TPaLQJ5_-159" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-149" value="&lt;span style=&quot;font-size: 12px; background-color: rgb(251, 251, 251);&quot;&gt;过滤非室分小区与经纬度异常&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="K5VGfCS9qDh2TPaLQJ5_-148" vertex="1" connectable="0">
          <mxGeometry x="0.6562" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-150" value="工参" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1035" y="130" width="50" height="20" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-151" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-152" target="K5VGfCS9qDh2TPaLQJ5_-110" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1325" y="1475" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-152" value="楼宇本期其他小区" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="1280" y="770" width="90" height="40" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-153" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-154" target="K5VGfCS9qDh2TPaLQJ5_-128" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="350" y="1880" />
              <mxPoint x="350" y="2140" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-154" value="楼宇本期其他小区" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="390" y="1850" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-155" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-156" target="K5VGfCS9qDh2TPaLQJ5_-128" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="450" y="2140" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-156" value="楼宇本期其他小区" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="390" y="1970" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-157" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-159" target="K5VGfCS9qDh2TPaLQJ5_-89" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-158" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-159" target="K5VGfCS9qDh2TPaLQJ5_-161" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-159" value="有效采样点" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="900" y="200" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-160" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="K5VGfCS9qDh2TPaLQJ5_-161" target="K5VGfCS9qDh2TPaLQJ5_-117" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1490" y="1700" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-161" value="eci-总采样点数" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1430" y="200" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="K5VGfCS9qDh2TPaLQJ5_-162" value="按小区分组汇聚" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="1170" y="208" width="110" height="30" as="geometry" />
        </mxCell>
        <mxCell id="3XyqPxl9EvhhYPwEuvdr-1" value="&lt;font color=&quot;#ff99cc&quot; style=&quot;forced-color-adjust: none; font-family: Helvetica; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: nowrap; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-size: 36px;&quot;&gt;这一版变化：&lt;br style=&quot;forced-color-adjust: none;&quot;&gt;1.不过滤ta值&lt;/font&gt;" style="text;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="545" y="60" width="240" height="110" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="GEyhdLnLrVXAVqWNowsq" name="v3-250207">
    <mxGraphModel dx="2074" dy="2366" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2xr_S9jXclznP_i9yBPG-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-2" target="2xr_S9jXclznP_i9yBPG-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-2" value="室分楼宇配置表" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="570" y="360" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-4" target="2xr_S9jXclznP_i9yBPG-26" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="630" y="1230" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-4" value="楼宇id+是否有室分覆盖配置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="570" y="560" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-6" target="2xr_S9jXclznP_i9yBPG-77" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-6" value="mdt采样点(整月)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="933.5" y="-200" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-8" target="2xr_S9jXclznP_i9yBPG-13" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-8" value="(参与图层关联的采样点)经纬度+eci" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="932.5" y="450" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-11" target="2xr_S9jXclznP_i9yBPG-13" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="795" y="530" />
              <mxPoint x="990" y="530" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-11" target="2xr_S9jXclznP_i9yBPG-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-11" value="楼宇图层" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="760" y="440" width="70" height="30" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-13" target="2xr_S9jXclznP_i9yBPG-17" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-13" value="楼宇id+eci+采样点个数" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="932.5" y="560" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VzkO-XyGBOYFsY5TMoHK-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-15" target="VzkO-XyGBOYFsY5TMoHK-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-15" value="统计楼宇内不同室分小区采样点占比" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="932.5" y="730" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="VzkO-XyGBOYFsY5TMoHK-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-17" target="2xr_S9jXclznP_i9yBPG-15" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-17" value="按楼宇id进行汇聚" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="932.5" y="650" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-19" target="2xr_S9jXclznP_i9yBPG-28" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1092.5" y="1840" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-19" value="楼宇本期&lt;font color=&quot;#ff0000&quot;&gt;主室分小区&lt;br&gt;（当有多个采样点数量为最大值的小区时，全部保留）&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="1032.5" y="1425" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-21" target="2xr_S9jXclznP_i9yBPG-28" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="972.5" y="1785" />
              <mxPoint x="902.5" y="1785" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-21" value="楼宇本期辅室分小区" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="912.5" y="1560" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-26" target="2xr_S9jXclznP_i9yBPG-21" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="882.5" y="1535" />
              <mxPoint x="972.5" y="1535" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-23" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="2xr_S9jXclznP_i9yBPG-22" vertex="1" connectable="0">
          <mxGeometry x="-0.0134" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-26" target="2xr_S9jXclznP_i9yBPG-51" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="755.5" y="1555" as="targetPoint" />
            <Array as="points">
              <mxPoint x="882.5" y="1535" />
              <mxPoint x="755.5" y="1535" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-25" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="2xr_S9jXclznP_i9yBPG-24" vertex="1" connectable="0">
          <mxGeometry x="0.0731" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-26" value="是否有室分配置" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="807.5" y="1425" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-27" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-28" target="2xr_S9jXclznP_i9yBPG-30" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-28" value="楼宇id+eci+role(主室分小区，辅室分小区，保留辅室分小区)+采样点个数" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="800" y="1805" width="205" height="70" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-29" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-30" target="2xr_S9jXclznP_i9yBPG-35" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-30" value="按eci进行分组" style="whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="842.5" y="1925" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-31" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="2xr_S9jXclznP_i9yBPG-35" target="2xr_S9jXclznP_i9yBPG-46" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="987.5" y="2205" as="targetPoint" />
            <Array as="points">
              <mxPoint x="902.5" y="2155" />
              <mxPoint x="1142.5" y="2155" />
              <mxPoint x="1142.5" y="3160" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-32" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="2xr_S9jXclznP_i9yBPG-31" vertex="1" connectable="0">
          <mxGeometry x="0.0772" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-33" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-35" target="P4f4RLM3L6pYrWJAQdvP-20" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="757.5" y="2205" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-34" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="2xr_S9jXclznP_i9yBPG-33" vertex="1" connectable="0">
          <mxGeometry x="0.2398" relative="1" as="geometry">
            <mxPoint y="8" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-35" value="同一个eci是否有多条数据(&lt;div&gt;即小区是否对应多个楼宇)&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="757.5" y="2025" width="290" height="80" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-38" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="P4f4RLM3L6pYrWJAQdvP-23" target="2xr_S9jXclznP_i9yBPG-72" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="757.5" y="2255" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-39" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="2xr_S9jXclznP_i9yBPG-38" vertex="1" connectable="0">
          <mxGeometry x="-0.0148" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-41" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-45" target="2xr_S9jXclznP_i9yBPG-46" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-42" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="2xr_S9jXclznP_i9yBPG-41" vertex="1" connectable="0">
          <mxGeometry x="-0.1857" y="3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-43" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-45" target="2xr_S9jXclznP_i9yBPG-74" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-44" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="2xr_S9jXclznP_i9yBPG-43" vertex="1" connectable="0">
          <mxGeometry x="-0.1199" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-45" value="同组内是否存在距离小于等于500米的楼宇" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="757.5" y="2910" width="290" height="80" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-46" value="输出结果" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="842.5" y="3129.96" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-47" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-51" target="2xr_S9jXclznP_i9yBPG-53" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-48" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="2xr_S9jXclznP_i9yBPG-47" vertex="1" connectable="0">
          <mxGeometry x="0.1503" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-49" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-51" target="2xr_S9jXclznP_i9yBPG-55" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-50" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="2xr_S9jXclznP_i9yBPG-49" vertex="1" connectable="0">
          <mxGeometry x="0.0085" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-51" value="上一期是否为&lt;div&gt;保留辅室分小区&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="682.5" y="1550" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-52" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-53" target="2xr_S9jXclznP_i9yBPG-28" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="682.5" y="1785" />
              <mxPoint x="902.5" y="1785" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-53" value="楼宇本期辅室分小区" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="622.5" y="1675" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-54" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-55" target="2xr_S9jXclznP_i9yBPG-28" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="842.5" y="1785" />
              <mxPoint x="902.5" y="1785" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-55" value="楼宇本期&lt;font color=&quot;#3399ff&quot;&gt;保留&lt;/font&gt;辅室分小区" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="782.5" y="1675" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-56" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-60" target="2xr_S9jXclznP_i9yBPG-65" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-57" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="2xr_S9jXclznP_i9yBPG-56" vertex="1" connectable="0">
          <mxGeometry x="-0.0778" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-58" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-60" target="2xr_S9jXclznP_i9yBPG-70" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-59" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="2xr_S9jXclznP_i9yBPG-58" vertex="1" connectable="0">
          <mxGeometry x="-0.0496" y="2" relative="1" as="geometry">
            <mxPoint y="2" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-60" value="小区采样点比例是否&lt;div&gt;大于平均值&lt;/div&gt;&lt;div&gt;(前一步过滤后剩余小区&lt;/div&gt;&lt;div&gt;的平均值)&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="895" y="1085" width="195" height="110" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-61" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-65" target="2xr_S9jXclznP_i9yBPG-19" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-62" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="2xr_S9jXclznP_i9yBPG-61" vertex="1" connectable="0">
          <mxGeometry x="0.0491" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-63" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-65" target="2xr_S9jXclznP_i9yBPG-26" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-64" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="2xr_S9jXclznP_i9yBPG-63" vertex="1" connectable="0">
          <mxGeometry x="0.0749" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-65" value="小区采样点数是否&lt;div&gt;为该楼宇内所有小区&lt;/div&gt;&lt;div&gt;的最大值&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="907.5" y="1235" width="170" height="100" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-66" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-68" target="2xr_S9jXclznP_i9yBPG-77" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-67" value="&lt;span style=&quot;font-size: 12px; background-color: rgb(251, 251, 251);&quot;&gt;过滤非室分小区与经纬度异常&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="2xr_S9jXclznP_i9yBPG-66" vertex="1" connectable="0">
          <mxGeometry x="0.6562" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-68" value="工参" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1068" y="-120" width="50" height="20" as="geometry" />
        </mxCell>
        <mxCell id="bWgflLddHZ_l5vCmNDEw-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-70" target="2xr_S9jXclznP_i9yBPG-46" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1225" y="3160" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-70" value="楼宇本期其他小区" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="1180" y="1120" width="90" height="40" as="geometry" />
        </mxCell>
        <mxCell id="P4f4RLM3L6pYrWJAQdvP-35" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-72" target="2xr_S9jXclznP_i9yBPG-46" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="467.5" y="2425" />
              <mxPoint x="467.5" y="3160" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-72" value="楼宇本期其他小区" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="567.5" y="2395" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-73" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-74" target="2xr_S9jXclznP_i9yBPG-46" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="627.5" y="3159.96" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-74" value="楼宇本期其他小区" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="567.5" y="2920" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ugc6NMm9geFCT1xXI2vU-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-77" target="ugc6NMm9geFCT1xXI2vU-2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-77" value="有效采样点" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="933.5" y="-50" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-78" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2xr_S9jXclznP_i9yBPG-79" target="2xr_S9jXclznP_i9yBPG-35" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1580" y="1950" />
              <mxPoint x="900" y="1950" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="2xr_S9jXclznP_i9yBPG-79" value="eci-总采样点数" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1500" y="340" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MPddsIakHZuMhrjRnG9R-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="MPddsIakHZuMhrjRnG9R-1" target="2xr_S9jXclznP_i9yBPG-8" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ugc6NMm9geFCT1xXI2vU-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="MPddsIakHZuMhrjRnG9R-1" target="2xr_S9jXclznP_i9yBPG-79" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MPddsIakHZuMhrjRnG9R-1" value="参与统计的小区以及采样点" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="932.5" y="340" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="znTYkoPuxOFB79cj6jwo-1" value="&lt;font color=&quot;#ff99cc&quot; style=&quot;font-size: 36px;&quot;&gt;这一版变化：&lt;br&gt;1.依旧不过滤ta值&lt;/font&gt;&lt;div style=&quot;font-size: 36px;&quot;&gt;&lt;font color=&quot;#ff99cc&quot; style=&quot;font-size: 36px;&quot;&gt;2.过滤掉总采样点数小于300的小区&lt;/font&gt;&lt;/div&gt;&lt;div style=&quot;font-size: 36px;&quot;&gt;&lt;font color=&quot;#ff99cc&quot; style=&quot;font-size: 36px;&quot;&gt;3.调整最后步骤的过滤规则&lt;/font&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="124" y="80" width="580" height="190" as="geometry" />
        </mxCell>
        <mxCell id="P4f4RLM3L6pYrWJAQdvP-22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="P4f4RLM3L6pYrWJAQdvP-20" target="P4f4RLM3L6pYrWJAQdvP-21" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="P4f4RLM3L6pYrWJAQdvP-20" value="按照采样点数对楼宇进行排序" style="whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="842.5" y="2205" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="P4f4RLM3L6pYrWJAQdvP-24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="P4f4RLM3L6pYrWJAQdvP-21" target="P4f4RLM3L6pYrWJAQdvP-23" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="P4f4RLM3L6pYrWJAQdvP-21" value="找到序号k，其中排序前k栋楼的采样点数总和大于或等于小区总采样点数的80%，而前k-1栋楼的采样点总和小于总采样点数的80%" style="whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="790" y="2295" width="225" height="60" as="geometry" />
        </mxCell>
        <mxCell id="bWgflLddHZ_l5vCmNDEw-14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="P4f4RLM3L6pYrWJAQdvP-23" target="bWgflLddHZ_l5vCmNDEw-13" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="bWgflLddHZ_l5vCmNDEw-25" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="bWgflLddHZ_l5vCmNDEw-14" vertex="1" connectable="0">
          <mxGeometry x="-0.0974" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P4f4RLM3L6pYrWJAQdvP-23" value="楼宇的序号是否小于等于k" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="757.5" y="2385" width="290" height="80" as="geometry" />
        </mxCell>
        <mxCell id="P4f4RLM3L6pYrWJAQdvP-28" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="P4f4RLM3L6pYrWJAQdvP-25" target="P4f4RLM3L6pYrWJAQdvP-27" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="P4f4RLM3L6pYrWJAQdvP-29" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="P4f4RLM3L6pYrWJAQdvP-28" vertex="1" connectable="0">
          <mxGeometry x="0.0254" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P4f4RLM3L6pYrWJAQdvP-30" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="P4f4RLM3L6pYrWJAQdvP-25" target="2xr_S9jXclznP_i9yBPG-45" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="bWgflLddHZ_l5vCmNDEw-21" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="P4f4RLM3L6pYrWJAQdvP-30" vertex="1" connectable="0">
          <mxGeometry x="-0.1324" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P4f4RLM3L6pYrWJAQdvP-25" value="楼宇采样点比例是否大于平均值&lt;div&gt;（前一步过滤后剩余楼宇采样点占比的平均值）&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="757.5" y="2710" width="290" height="100" as="geometry" />
        </mxCell>
        <mxCell id="bWgflLddHZ_l5vCmNDEw-22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="P4f4RLM3L6pYrWJAQdvP-27" target="2xr_S9jXclznP_i9yBPG-46" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="542.5" y="2760" />
              <mxPoint x="542.5" y="3160" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="P4f4RLM3L6pYrWJAQdvP-27" value="楼宇本期其他小区" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="567.5" y="2730" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ugc6NMm9geFCT1xXI2vU-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ugc6NMm9geFCT1xXI2vU-2" target="ugc6NMm9geFCT1xXI2vU-5" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ugc6NMm9geFCT1xXI2vU-7" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ugc6NMm9geFCT1xXI2vU-6" vertex="1" connectable="0">
          <mxGeometry x="-0.1578" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ugc6NMm9geFCT1xXI2vU-2" value="小区排序是否在&lt;div&gt;前90%&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="918.5" y="70" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="ugc6NMm9geFCT1xXI2vU-4" value="按小区分组汇聚，按采样点数排序" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="890" y="30" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ugc6NMm9geFCT1xXI2vU-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ugc6NMm9geFCT1xXI2vU-5" target="MPddsIakHZuMhrjRnG9R-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ugc6NMm9geFCT1xXI2vU-9" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ugc6NMm9geFCT1xXI2vU-8" vertex="1" connectable="0">
          <mxGeometry x="-0.1743" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ugc6NMm9geFCT1xXI2vU-5" value="小区采样点数是否&lt;div&gt;大于300&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="912.5" y="200" width="162" height="80" as="geometry" />
        </mxCell>
        <mxCell id="bWgflLddHZ_l5vCmNDEw-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="bWgflLddHZ_l5vCmNDEw-1" target="bWgflLddHZ_l5vCmNDEw-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="bWgflLddHZ_l5vCmNDEw-6" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="bWgflLddHZ_l5vCmNDEw-5" vertex="1" connectable="0">
          <mxGeometry x="-0.0993" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VzkO-XyGBOYFsY5TMoHK-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="bWgflLddHZ_l5vCmNDEw-1" target="2xr_S9jXclznP_i9yBPG-60" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="bWgflLddHZ_l5vCmNDEw-1" value="小区采样点数占比&lt;div&gt;是否大于等于1%&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="917.5" y="960" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="bWgflLddHZ_l5vCmNDEw-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="bWgflLddHZ_l5vCmNDEw-4" target="2xr_S9jXclznP_i9yBPG-46" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1360" y="3160" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="bWgflLddHZ_l5vCmNDEw-4" value="楼宇本期其他小区" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="1310" y="980" width="90" height="40" as="geometry" />
        </mxCell>
        <mxCell id="bWgflLddHZ_l5vCmNDEw-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="bWgflLddHZ_l5vCmNDEw-13" target="bWgflLddHZ_l5vCmNDEw-15" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="bWgflLddHZ_l5vCmNDEw-17" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="bWgflLddHZ_l5vCmNDEw-16" vertex="1" connectable="0">
          <mxGeometry x="0.0308" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bWgflLddHZ_l5vCmNDEw-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="bWgflLddHZ_l5vCmNDEw-13" target="P4f4RLM3L6pYrWJAQdvP-25" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="bWgflLddHZ_l5vCmNDEw-20" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="bWgflLddHZ_l5vCmNDEw-19" vertex="1" connectable="0">
          <mxGeometry x="-0.1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bWgflLddHZ_l5vCmNDEw-13" value="落到楼宇的采样点占小区总采样点比例&lt;div&gt;是否大于等于1%&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="757.5" y="2530" width="290" height="100" as="geometry" />
        </mxCell>
        <mxCell id="bWgflLddHZ_l5vCmNDEw-24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="bWgflLddHZ_l5vCmNDEw-15" target="2xr_S9jXclznP_i9yBPG-46" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="492.5" y="2580" />
              <mxPoint x="492.5" y="3160" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="bWgflLddHZ_l5vCmNDEw-15" value="楼宇本期其他小区" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="572.5" y="2550" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VzkO-XyGBOYFsY5TMoHK-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="VzkO-XyGBOYFsY5TMoHK-1" target="bWgflLddHZ_l5vCmNDEw-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VzkO-XyGBOYFsY5TMoHK-9" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="VzkO-XyGBOYFsY5TMoHK-7" vertex="1" connectable="0">
          <mxGeometry x="-0.2756" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VzkO-XyGBOYFsY5TMoHK-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="VzkO-XyGBOYFsY5TMoHK-1" target="VzkO-XyGBOYFsY5TMoHK-10" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VzkO-XyGBOYFsY5TMoHK-12" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="VzkO-XyGBOYFsY5TMoHK-11" vertex="1" connectable="0">
          <mxGeometry x="-0.0769" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VzkO-XyGBOYFsY5TMoHK-1" value="小区采样点数是否大于10" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="917.5" y="820" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="VzkO-XyGBOYFsY5TMoHK-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="VzkO-XyGBOYFsY5TMoHK-10" target="2xr_S9jXclznP_i9yBPG-46" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1480" y="3160" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="VzkO-XyGBOYFsY5TMoHK-10" value="楼宇本期其他小区" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="1430" y="840" width="90" height="40" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="7_LtikaFX29XhICjCVbf" name="终版-250325">
    <mxGraphModel dx="1659" dy="2127" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ITvMAcQWO9AIt8AeYUK5-6" target="ITvMAcQWO9AIt8AeYUK5-73" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="AGXAdr_Suaa3wvtiSRCR-2" value="过滤经纬度异常以及非室分小区和地铁小区" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ITvMAcQWO9AIt8AeYUK5-5" vertex="1" connectable="0">
          <mxGeometry x="-0.4686" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-6" value="mdt采样点(整月)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="533.42" y="-1130" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ITvMAcQWO9AIt8AeYUK5-8" target="ITvMAcQWO9AIt8AeYUK5-13" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-8" value="(参与图层关联的采样点)经纬度+eci" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="532.17" y="-420" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ITvMAcQWO9AIt8AeYUK5-11" target="ITvMAcQWO9AIt8AeYUK5-13" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="395" y="-340" />
              <mxPoint x="590" y="-340" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-11" value="楼宇图层" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="338.41999999999996" y="-420" width="110" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ITvMAcQWO9AIt8AeYUK5-13" target="ITvMAcQWO9AIt8AeYUK5-30" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="592.17" y="-220" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-13" value="楼宇id+eci+采样点个数" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="532.17" y="-310" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="4B1zp5WjyWPEuoMAcedZ-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ITvMAcQWO9AIt8AeYUK5-21" target="4B1zp5WjyWPEuoMAcedZ-5" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="572" y="1480" />
              <mxPoint x="477" y="1480" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-21" value="本期小区次覆盖楼宇" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="512.17" y="1230" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="lcQC1VHsUI9fVKV7nXj_-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ITvMAcQWO9AIt8AeYUK5-26" target="ITvMAcQWO9AIt8AeYUK5-48" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="lcQC1VHsUI9fVKV7nXj_-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ITvMAcQWO9AIt8AeYUK5-26" target="ITvMAcQWO9AIt8AeYUK5-21" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="lcQC1VHsUI9fVKV7nXj_-5" value="&lt;font color=&quot;#ff0000&quot;&gt;YES&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="lcQC1VHsUI9fVKV7nXj_-3" vertex="1" connectable="0">
          <mxGeometry x="0.0961" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-26" value="是否有室分配置" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="401.91999999999996" y="1054.96" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-29" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="ITvMAcQWO9AIt8AeYUK5-30" target="rkA0I9zYDR3i-FJ9FkVX-3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="592.5300000000001" y="-80" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-30" value="按eci进行分组" style="whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="532.53" y="-195" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-44" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ITvMAcQWO9AIt8AeYUK5-48" target="ITvMAcQWO9AIt8AeYUK5-50" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-45" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" parent="ITvMAcQWO9AIt8AeYUK5-44" vertex="1" connectable="0">
          <mxGeometry x="0.1503" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-46" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ITvMAcQWO9AIt8AeYUK5-48" target="ITvMAcQWO9AIt8AeYUK5-52" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-47" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" parent="ITvMAcQWO9AIt8AeYUK5-46" vertex="1" connectable="0">
          <mxGeometry x="0.0085" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-48" value="上&lt;font color=&quot;#ff6666&quot;&gt;两&lt;/font&gt;期是否为&lt;div&gt;小区次覆盖楼宇&lt;br&gt;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="282.66999999999996" y="1230" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="lcQC1VHsUI9fVKV7nXj_-6" value="&lt;b style=&quot;forced-color-adjust: none; color: rgb(255, 102, 102); font-family: Helvetica; font-size: 11px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: nowrap; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;&quot;&gt;更新至配置表中&lt;/b&gt;" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ITvMAcQWO9AIt8AeYUK5-50" target="FmYWR0Z4Jlu1sFn8w6Lr-6" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="180" y="1385" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="4B1zp5WjyWPEuoMAcedZ-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ITvMAcQWO9AIt8AeYUK5-50" target="4B1zp5WjyWPEuoMAcedZ-5" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="283" y="1480" />
              <mxPoint x="477" y="1480" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-50" value="本期小区次覆盖楼宇" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="222.66999999999996" y="1355" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="4B1zp5WjyWPEuoMAcedZ-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ITvMAcQWO9AIt8AeYUK5-52" target="4B1zp5WjyWPEuoMAcedZ-5" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="443" y="1480" />
              <mxPoint x="477" y="1480" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-52" value="小区本期&lt;font color=&quot;#3399ff&quot;&gt;保留次覆盖楼宇&lt;/font&gt;" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="382.66999999999996" y="1355" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="AGXAdr_Suaa3wvtiSRCR-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ITvMAcQWO9AIt8AeYUK5-65" target="ITvMAcQWO9AIt8AeYUK5-73" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-65" value="电联4G非室分小区&amp;amp;&lt;br&gt;地铁小区" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="674.17" y="-1000" width="115.83" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-72" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ITvMAcQWO9AIt8AeYUK5-73" target="ITvMAcQWO9AIt8AeYUK5-96" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-73" value="按ECI进行小区采样点计算" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="533.17" y="-920" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MDYvKz7qLFOV7e8ZzRY8-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="ITvMAcQWO9AIt8AeYUK5-75" target="rkA0I9zYDR3i-FJ9FkVX-28" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-75" value="eci-总采样点数" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="720" y="-520" width="90.33" height="40" as="geometry" />
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-76" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ITvMAcQWO9AIt8AeYUK5-78" target="ITvMAcQWO9AIt8AeYUK5-8" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-77" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ITvMAcQWO9AIt8AeYUK5-78" target="ITvMAcQWO9AIt8AeYUK5-75" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-78" value="参与统计的小区以及采样点" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="532.17" y="-530" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-94" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ITvMAcQWO9AIt8AeYUK5-96" target="ITvMAcQWO9AIt8AeYUK5-100" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-95" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" parent="ITvMAcQWO9AIt8AeYUK5-94" vertex="1" connectable="0">
          <mxGeometry x="-0.1578" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="lcQC1VHsUI9fVKV7nXj_-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ITvMAcQWO9AIt8AeYUK5-96" target="lcQC1VHsUI9fVKV7nXj_-8" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="lcQC1VHsUI9fVKV7nXj_-22" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" parent="lcQC1VHsUI9fVKV7nXj_-9" vertex="1" connectable="0">
          <mxGeometry x="0.0189" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-96" value="小区总采样点排序在&lt;div&gt;前90%(eci)&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="518.17" y="-800" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-97" value="按小区分组汇聚，按采样点数排序" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="489.66999999999996" y="-840" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-98" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ITvMAcQWO9AIt8AeYUK5-100" target="ITvMAcQWO9AIt8AeYUK5-78" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-99" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" parent="ITvMAcQWO9AIt8AeYUK5-98" vertex="1" connectable="0">
          <mxGeometry x="-0.1743" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="lcQC1VHsUI9fVKV7nXj_-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ITvMAcQWO9AIt8AeYUK5-100" target="lcQC1VHsUI9fVKV7nXj_-10" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ITvMAcQWO9AIt8AeYUK5-100" value="小区采样点总数&lt;div&gt;&lt;font color=&quot;#ff0000&quot;&gt;大于等于&lt;/font&gt;300&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="512.17" y="-670" width="162" height="80" as="geometry" />
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-3" value="统计eci落入楼宇内的采样点数" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="529.17" y="-93" width="126.5" height="43" as="geometry" />
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-6" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" target="rkA0I9zYDR3i-FJ9FkVX-28" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="592.17" y="-50" as="sourcePoint" />
            <mxPoint x="594.1700000000004" y="-7" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="rkA0I9zYDR3i-FJ9FkVX-19" target="rkA0I9zYDR3i-FJ9FkVX-20" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-17" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" parent="rkA0I9zYDR3i-FJ9FkVX-16" vertex="1" connectable="0">
          <mxGeometry x="0.0308" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-19" value="小区落到某楼宇的采样点数&lt;br&gt;占小区总采样点比例&lt;div&gt;&lt;font color=&quot;#ff0000&quot;&gt;大于等于&lt;/font&gt;1%&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="448.41999999999996" y="180" width="290" height="100" as="geometry" />
        </mxCell>
        <mxCell id="FmYWR0Z4Jlu1sFn8w6Lr-10" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=#FF0000;" parent="1" source="rkA0I9zYDR3i-FJ9FkVX-20" target="FmYWR0Z4Jlu1sFn8w6Lr-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="49.670000000000016" y="1512.6666666666665" as="targetPoint" />
            <Array as="points">
              <mxPoint x="50" y="230" />
              <mxPoint x="50" y="1920" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-20" value="其他小区d" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="115.92000000000002" y="199.05999999999995" width="123.75" height="61.88" as="geometry" />
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="rkA0I9zYDR3i-FJ9FkVX-24" target="rkA0I9zYDR3i-FJ9FkVX-25" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-22" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" parent="rkA0I9zYDR3i-FJ9FkVX-21" vertex="1" connectable="0">
          <mxGeometry x="0.0254" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-24" value="小区落入某楼宇采样点比例&lt;br&gt;&lt;font color=&quot;#ff0000&quot;&gt;大于等于&lt;/font&gt;小区覆盖所有楼宇的采样点比例平均值&lt;div&gt;（过滤后剩余楼宇采样点占比的平均值）&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="414" y="360" width="366.58" height="100" as="geometry" />
        </mxCell>
        <mxCell id="FmYWR0Z4Jlu1sFn8w6Lr-12" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=#FF0000;" parent="1" source="rkA0I9zYDR3i-FJ9FkVX-25" target="FmYWR0Z4Jlu1sFn8w6Lr-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="39.670000000000016" y="410" as="targetPoint" />
            <Array as="points">
              <mxPoint x="50" y="410" />
              <mxPoint x="50" y="1920" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-25" value="其他小区e" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="119.67000000000002" y="380" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-26" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" target="rkA0I9zYDR3i-FJ9FkVX-24" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="598.42" y="280.00000000000045" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-27" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" parent="rkA0I9zYDR3i-FJ9FkVX-26" vertex="1" connectable="0">
          <mxGeometry x="-0.1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-28" value="小区落入楼宇的采样点数占&lt;br&gt;小区总采样点比例&lt;br&gt;&lt;font color=&quot;#ff0000&quot;&gt;大于等于&lt;/font&gt;90%" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="448.41999999999996" y="-3" width="290" height="100" as="geometry" />
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-29" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" target="rkA0I9zYDR3i-FJ9FkVX-31" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="450.41999999999996" y="47" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-30" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" parent="rkA0I9zYDR3i-FJ9FkVX-29" vertex="1" connectable="0">
          <mxGeometry x="0.0308" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FmYWR0Z4Jlu1sFn8w6Lr-9" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=#FF0000;" parent="1" source="rkA0I9zYDR3i-FJ9FkVX-31" target="FmYWR0Z4Jlu1sFn8w6Lr-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="79.67000000000002" y="1616" as="targetPoint" />
            <Array as="points">
              <mxPoint x="50" y="47" />
              <mxPoint x="50" y="1920" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-31" value="其他小区c" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="119.67000000000002" y="17" width="125.75" height="60" as="geometry" />
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-32" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="rkA0I9zYDR3i-FJ9FkVX-28" target="rkA0I9zYDR3i-FJ9FkVX-19" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="595.06" y="110" as="sourcePoint" />
            <mxPoint x="609.67" y="160" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-33" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" parent="rkA0I9zYDR3i-FJ9FkVX-32" vertex="1" connectable="0">
          <mxGeometry x="-0.1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-34" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="rkA0I9zYDR3i-FJ9FkVX-35" target="rkA0I9zYDR3i-FJ9FkVX-36" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-35" value="按照小区落入单楼宇的采样点数进行降序排序(eci+buildingId)" style="whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="538.42" y="518" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-36" value="找到序号k，其中排序前k栋楼的采样点数总和大于或等于小区总采样点数的80%，而前k-1栋楼的采样点总和小于总采样点数的80%" style="whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="485.91999999999996" y="615" width="225" height="60" as="geometry" />
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-37" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="rkA0I9zYDR3i-FJ9FkVX-24" target="rkA0I9zYDR3i-FJ9FkVX-35" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="601.42" y="446.0000000000009" as="sourcePoint" />
            <mxPoint x="601.42" y="526" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-38" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" parent="rkA0I9zYDR3i-FJ9FkVX-37" vertex="1" connectable="0">
          <mxGeometry x="-0.1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-40" value="楼宇的序号是否小于等于k" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="455.66999999999996" y="750" width="290" height="80" as="geometry" />
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-41" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" target="rkA0I9zYDR3i-FJ9FkVX-43" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="459.41999999999996" y="790" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-42" value="&lt;font color=&quot;#ff0000&quot;&gt;NO&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="rkA0I9zYDR3i-FJ9FkVX-41" vertex="1" connectable="0">
          <mxGeometry x="0.0254" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FmYWR0Z4Jlu1sFn8w6Lr-13" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=#FF0000;" parent="1" source="rkA0I9zYDR3i-FJ9FkVX-43" target="FmYWR0Z4Jlu1sFn8w6Lr-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="39.670000000000016" y="790" as="targetPoint" />
            <Array as="points">
              <mxPoint x="50" y="790" />
              <mxPoint x="50" y="1920" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-43" value="其他小区f" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="119.67000000000002" y="760" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="rkA0I9zYDR3i-FJ9FkVX-44" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" target="rkA0I9zYDR3i-FJ9FkVX-40" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="599.67" y="680" as="sourcePoint" />
            <mxPoint x="608.4700000000001" y="625" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="6yeWvR_L3cx-1Ldf3kH0-1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="599.67" y="830" as="sourcePoint" />
            <mxPoint x="599.67" y="890" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="6yeWvR_L3cx-1Ldf3kH0-2" value="&lt;font color=&quot;#ff0000&quot;&gt;YES&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="6yeWvR_L3cx-1Ldf3kH0-1" vertex="1" connectable="0">
          <mxGeometry x="-0.1857" y="3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="HvnwMhe4w8NKkc8JX08S-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="6yeWvR_L3cx-1Ldf3kH0-3" target="ITvMAcQWO9AIt8AeYUK5-26" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="HvnwMhe4w8NKkc8JX08S-6" value="&lt;font color=&quot;#ff0000&quot;&gt;NO&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="HvnwMhe4w8NKkc8JX08S-1" vertex="1" connectable="0">
          <mxGeometry x="0.0206" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="HvnwMhe4w8NKkc8JX08S-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="6yeWvR_L3cx-1Ldf3kH0-3" target="6yeWvR_L3cx-1Ldf3kH0-10" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="HvnwMhe4w8NKkc8JX08S-5" value="&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;YES&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="HvnwMhe4w8NKkc8JX08S-2" vertex="1" connectable="0">
          <mxGeometry x="0.1242" y="2" relative="1" as="geometry">
            <mxPoint y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="6yeWvR_L3cx-1Ldf3kH0-3" value="落到楼宇的采样点占小区总采样点比例&lt;br&gt;是否最大值" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="455.66999999999996" y="890" width="290" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Puwh0CWqI78At90M4v6A-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="6yeWvR_L3cx-1Ldf3kH0-10" target="4B1zp5WjyWPEuoMAcedZ-5" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="693" y="1480" />
              <mxPoint x="477" y="1480" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="6yeWvR_L3cx-1Ldf3kH0-10" value="小区主覆盖楼宇" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="632.67" y="1054.96" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="FmYWR0Z4Jlu1sFn8w6Lr-1" value="输出结果" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="416.91999999999996" y="1890" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MDYvKz7qLFOV7e8ZzRY8-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="FmYWR0Z4Jlu1sFn8w6Lr-6" target="ITvMAcQWO9AIt8AeYUK5-26" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="FmYWR0Z4Jlu1sFn8w6Lr-6" value="室分楼宇配置表" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="119.67000000000002" y="1064.96" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="lcQC1VHsUI9fVKV7nXj_-4" value="&lt;font color=&quot;#ff0000&quot;&gt;NO&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="1" vertex="1" connectable="0">
          <mxGeometry x="551.9182758620686" y="1023.5" as="geometry">
            <mxPoint x="-141" y="156" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="lcQC1VHsUI9fVKV7nXj_-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="lcQC1VHsUI9fVKV7nXj_-8" target="FmYWR0Z4Jlu1sFn8w6Lr-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="50" y="-760" />
              <mxPoint x="50" y="1920" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="lcQC1VHsUI9fVKV7nXj_-8" value="其他小区a" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="121.67000000000007" y="-790.94" width="123.75" height="61.88" as="geometry" />
        </mxCell>
        <mxCell id="g-9qDMQBHfxX44uXlH_p-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="lcQC1VHsUI9fVKV7nXj_-10" target="FmYWR0Z4Jlu1sFn8w6Lr-1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="50" y="-630" />
              <mxPoint x="50" y="1920" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="lcQC1VHsUI9fVKV7nXj_-10" value="其他小区b" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="121.67000000000007" y="-660.94" width="123.75" height="61.88" as="geometry" />
        </mxCell>
        <mxCell id="lcQC1VHsUI9fVKV7nXj_-17" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" parent="1" vertex="1" connectable="0">
          <mxGeometry x="357.66788613195024" y="60.000000000000455" as="geometry">
            <mxPoint x="21" y="-689" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="4B1zp5WjyWPEuoMAcedZ-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="4B1zp5WjyWPEuoMAcedZ-5" target="4B1zp5WjyWPEuoMAcedZ-11" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="4B1zp5WjyWPEuoMAcedZ-13" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" parent="4B1zp5WjyWPEuoMAcedZ-12" vertex="1" connectable="0">
          <mxGeometry x="0.0229" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="4B1zp5WjyWPEuoMAcedZ-15" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="4B1zp5WjyWPEuoMAcedZ-5" target="4B1zp5WjyWPEuoMAcedZ-14" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="4B1zp5WjyWPEuoMAcedZ-16" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" parent="4B1zp5WjyWPEuoMAcedZ-15" vertex="1" connectable="0">
          <mxGeometry x="-0.0936" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="4B1zp5WjyWPEuoMAcedZ-5" value="&lt;div&gt;小区对应的结果&lt;/div&gt;&lt;div&gt;记录条数小于等于5&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="377.88" y="1560" width="198.08" height="80" as="geometry" />
        </mxCell>
        <mxCell id="4B1zp5WjyWPEuoMAcedZ-29" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="4B1zp5WjyWPEuoMAcedZ-11" target="FmYWR0Z4Jlu1sFn8w6Lr-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="50" y="1600" />
              <mxPoint x="50" y="1920" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="4B1zp5WjyWPEuoMAcedZ-11" value="怀疑有误a(小区对应的所有楼宇结果)" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="119.66999999999994" y="1570" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Puwh0CWqI78At90M4v6A-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="4B1zp5WjyWPEuoMAcedZ-14" target="4B1zp5WjyWPEuoMAcedZ-25" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Puwh0CWqI78At90M4v6A-2" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" parent="Puwh0CWqI78At90M4v6A-1" vertex="1" connectable="0">
          <mxGeometry x="-0.0127" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Puwh0CWqI78At90M4v6A-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="4B1zp5WjyWPEuoMAcedZ-14" target="FmYWR0Z4Jlu1sFn8w6Lr-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Puwh0CWqI78At90M4v6A-4" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" parent="Puwh0CWqI78At90M4v6A-3" vertex="1" connectable="0">
          <mxGeometry x="-0.1955" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="4B1zp5WjyWPEuoMAcedZ-14" value="小区落到&lt;font color=&quot;#ff0000&quot;&gt;主覆盖&lt;/font&gt;楼宇的采样点&lt;br&gt;占小区总采样点比例大于等于20%" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
          <mxGeometry x="350.86" y="1720" width="252.12" height="110" as="geometry" />
        </mxCell>
        <mxCell id="Puwh0CWqI78At90M4v6A-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="4B1zp5WjyWPEuoMAcedZ-25" target="FmYWR0Z4Jlu1sFn8w6Lr-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="50" y="1775" />
              <mxPoint x="50" y="1920" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="4B1zp5WjyWPEuoMAcedZ-25" value="怀疑有误b(小区对应的所有楼宇结果)" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="125.41999999999996" y="1745" width="120" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram name="250327" id="4KNVBkP-Um2XPurJhlud">
    <mxGraphModel dx="2440" dy="2577" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-0" />
        <mxCell id="dljHi8mAp1HHXRXAOkaB-1" parent="dljHi8mAp1HHXRXAOkaB-0" />
        <mxCell id="2QlyW9KXMpESUc1ni_Ah-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-4" target="dljHi8mAp1HHXRXAOkaB-42">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-4" value="楼宇id+eci+采样点数（月粒度）" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="663.17" y="-530" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-12" target="dljHi8mAp1HHXRXAOkaB-102">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="700" y="1473" />
              <mxPoint x="605" y="1473" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-12" value="本期小区次覆盖楼宇" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="640.17" y="1223" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-16" target="dljHi8mAp1HHXRXAOkaB-23">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-16" target="dljHi8mAp1HHXRXAOkaB-12">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-15" value="&lt;font color=&quot;#ff0000&quot;&gt;YES&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="dljHi8mAp1HHXRXAOkaB-14">
          <mxGeometry x="0.0961" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-16" value="是否有室分配置" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="529.92" y="1047.96" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-19" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-23" target="dljHi8mAp1HHXRXAOkaB-26">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-20" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" vertex="1" connectable="0" parent="dljHi8mAp1HHXRXAOkaB-19">
          <mxGeometry x="0.1503" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-23" target="dljHi8mAp1HHXRXAOkaB-28">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-22" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" vertex="1" connectable="0" parent="dljHi8mAp1HHXRXAOkaB-21">
          <mxGeometry x="0.0085" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-23" value="上&lt;font color=&quot;#ff6666&quot;&gt;两&lt;/font&gt;期是否为&lt;div&gt;小区次覆盖楼宇&lt;br&gt;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="410.66999999999996" y="1223" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-24" value="&lt;b style=&quot;forced-color-adjust: none; color: rgb(255, 102, 102); font-family: Helvetica; font-size: 11px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: nowrap; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;&quot;&gt;更新至配置表中&lt;/b&gt;" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-26" target="dljHi8mAp1HHXRXAOkaB-91">
          <mxGeometry x="0.4463" y="-20" relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="210" y="1378" />
              <mxPoint x="210" y="1088" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-26" target="dljHi8mAp1HHXRXAOkaB-102">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="411" y="1473" />
              <mxPoint x="605" y="1473" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-26" value="本期小区次覆盖楼宇" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="350.66999999999996" y="1348" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-28" target="dljHi8mAp1HHXRXAOkaB-102">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="571" y="1473" />
              <mxPoint x="605" y="1473" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-28" value="小区本期&lt;font color=&quot;#3399ff&quot;&gt;保留次覆盖楼宇&lt;/font&gt;" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="510.66999999999996" y="1348" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-38" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-42" target="dljHi8mAp1HHXRXAOkaB-47">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-39" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" vertex="1" connectable="0" parent="dljHi8mAp1HHXRXAOkaB-38">
          <mxGeometry x="-0.1578" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-40" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-42" target="dljHi8mAp1HHXRXAOkaB-94">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-41" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" vertex="1" connectable="0" parent="dljHi8mAp1HHXRXAOkaB-40">
          <mxGeometry x="0.0189" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-42" value="小区总采样点排序在&lt;div&gt;前90%(eci)&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="648.17" y="-290" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-43" value="按eci汇聚，&lt;br&gt;按采样点数排序" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="667.17" y="-395" width="110" height="40" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-46" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-47" target="dljHi8mAp1HHXRXAOkaB-96">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="2QlyW9KXMpESUc1ni_Ah-18" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" vertex="1" connectable="0" parent="dljHi8mAp1HHXRXAOkaB-46">
          <mxGeometry x="-0.0003" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2QlyW9KXMpESUc1ni_Ah-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-47" target="dljHi8mAp1HHXRXAOkaB-62">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="2QlyW9KXMpESUc1ni_Ah-17" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" vertex="1" connectable="0" parent="2QlyW9KXMpESUc1ni_Ah-9">
          <mxGeometry x="-0.1504" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-47" value="小区采样点总数&lt;div&gt;&lt;font color=&quot;#ff0000&quot;&gt;大于等于&lt;/font&gt;300&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="642.17" y="-160" width="162" height="80" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-50" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-52" target="dljHi8mAp1HHXRXAOkaB-54">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-51" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" vertex="1" connectable="0" parent="dljHi8mAp1HHXRXAOkaB-50">
          <mxGeometry x="0.0308" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-52" value="小区落到某楼宇的采样点数&lt;br&gt;占小区总采样点比例&lt;div&gt;&lt;font color=&quot;#ff0000&quot;&gt;大于等于&lt;/font&gt;1%&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="577.17" y="173" width="290" height="100" as="geometry" />
        </mxCell>
        <mxCell id="r5KGS2CTENY4qoHPWnlz-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-54" target="dljHi8mAp1HHXRXAOkaB-89">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="170" y="223" />
              <mxPoint x="170" y="1913" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-54" value="其他小区d(小区-楼宇对应结果)" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="243.92000000000002" y="192.05999999999995" width="123.75" height="61.88" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-55" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-57" target="dljHi8mAp1HHXRXAOkaB-59">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-56" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" vertex="1" connectable="0" parent="dljHi8mAp1HHXRXAOkaB-55">
          <mxGeometry x="0.0254" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-57" value="小区落入某楼宇采样点比例&lt;br&gt;&lt;font color=&quot;#ff0000&quot;&gt;大于等于&lt;/font&gt;小区覆盖所有楼宇的采样点比例平均值&lt;div&gt;（过滤后剩余楼宇采样点占比的平均值）&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="542" y="353" width="366.58" height="100" as="geometry" />
        </mxCell>
        <mxCell id="r5KGS2CTENY4qoHPWnlz-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-59" target="dljHi8mAp1HHXRXAOkaB-89">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="170" y="403" />
              <mxPoint x="170" y="1913" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-59" value="其他小区e(小区-楼宇对应结果)" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="247.67000000000002" y="373" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-60" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" target="dljHi8mAp1HHXRXAOkaB-57">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="726.42" y="273.00000000000045" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-61" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" vertex="1" connectable="0" parent="dljHi8mAp1HHXRXAOkaB-60">
          <mxGeometry x="-0.1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-62" value="小区落入楼宇的采样点数占&lt;br&gt;小区总采样点比例&lt;br&gt;&lt;font color=&quot;#ff0000&quot;&gt;大于等于&lt;/font&gt;90%" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="578.17" y="-10" width="290" height="100" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-63" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" target="dljHi8mAp1HHXRXAOkaB-66">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="578.42" y="40" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-64" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" vertex="1" connectable="0" parent="dljHi8mAp1HHXRXAOkaB-63">
          <mxGeometry x="0.0308" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="r5KGS2CTENY4qoHPWnlz-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-66" target="dljHi8mAp1HHXRXAOkaB-89">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="170" y="40" />
              <mxPoint x="170" y="1913" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-66" value="其他小区c(小区对应的所有楼宇结果)" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="247.67000000000002" y="10" width="125.75" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-67" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-62" target="dljHi8mAp1HHXRXAOkaB-52">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="723.06" y="103" as="sourcePoint" />
            <mxPoint x="737.67" y="153" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-68" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" vertex="1" connectable="0" parent="dljHi8mAp1HHXRXAOkaB-67">
          <mxGeometry x="-0.1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-69" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-70" target="dljHi8mAp1HHXRXAOkaB-71">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-70" value="按照小区落入单楼宇的采样点数进行降序排序(eci+buildingId)" style="whiteSpace=wrap;html=1;rounded=1;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="666.42" y="511" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-71" value="找到序号k，其中排序前k栋楼的采样点数总和大于或等于小区总采样点数的80%，而前k-1栋楼的采样点总和小于总采样点数的80%" style="whiteSpace=wrap;html=1;rounded=1;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="613.92" y="608" width="225" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-72" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-57" target="dljHi8mAp1HHXRXAOkaB-70">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="729.42" y="439.0000000000009" as="sourcePoint" />
            <mxPoint x="729.42" y="519" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-73" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" vertex="1" connectable="0" parent="dljHi8mAp1HHXRXAOkaB-72">
          <mxGeometry x="-0.1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-74" value="楼宇的序号是否小于等于k" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="583.67" y="743" width="290" height="80" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-75" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" target="dljHi8mAp1HHXRXAOkaB-78">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="587.42" y="783" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-76" value="&lt;font color=&quot;#ff0000&quot;&gt;NO&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="dljHi8mAp1HHXRXAOkaB-75">
          <mxGeometry x="0.0254" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="r5KGS2CTENY4qoHPWnlz-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-78" target="dljHi8mAp1HHXRXAOkaB-89">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="170" y="783" />
              <mxPoint x="170" y="1913" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-78" value="其他小区f(小区-楼宇对应结果)" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="247.67000000000002" y="753" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-79" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" target="dljHi8mAp1HHXRXAOkaB-74">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="727.67" y="673" as="sourcePoint" />
            <mxPoint x="736.4700000000001" y="618" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-80" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="727.67" y="823" as="sourcePoint" />
            <mxPoint x="727.67" y="883" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-81" value="&lt;font color=&quot;#ff0000&quot;&gt;YES&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="dljHi8mAp1HHXRXAOkaB-80">
          <mxGeometry x="-0.1857" y="3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-82" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-86" target="dljHi8mAp1HHXRXAOkaB-16">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-83" value="&lt;font color=&quot;#ff0000&quot;&gt;NO&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="dljHi8mAp1HHXRXAOkaB-82">
          <mxGeometry x="0.0206" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-84" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-86" target="dljHi8mAp1HHXRXAOkaB-88">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-85" value="&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;YES&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="dljHi8mAp1HHXRXAOkaB-84">
          <mxGeometry x="0.1242" y="2" relative="1" as="geometry">
            <mxPoint y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-86" value="落到楼宇的采样点占小区总采样点比例&lt;br&gt;是否最大值" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="583.67" y="883" width="290" height="80" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-87" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-88" target="dljHi8mAp1HHXRXAOkaB-102">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="821" y="1473" />
              <mxPoint x="605" y="1473" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-88" value="小区主覆盖楼宇" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="760.67" y="1047.96" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-89" value="输出结果" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="544.92" y="1883" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-90" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-91" target="dljHi8mAp1HHXRXAOkaB-16">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-91" value="室分楼宇配置表" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="247.67000000000002" y="1057.96" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-92" value="&lt;font color=&quot;#ff0000&quot;&gt;NO&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="679.9182758620686" y="1016.5" as="geometry">
            <mxPoint x="-141" y="156" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="r5KGS2CTENY4qoHPWnlz-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-94" target="dljHi8mAp1HHXRXAOkaB-89">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="170" y="-250" />
              <mxPoint x="170" y="1913" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-94" value="其他小区a(仅输出一条结果，楼宇相关信息置默认值)" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="251.67000000000007" y="-280.94000000000005" width="123.75" height="61.88" as="geometry" />
        </mxCell>
        <mxCell id="r5KGS2CTENY4qoHPWnlz-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-96" target="dljHi8mAp1HHXRXAOkaB-89">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="170" y="-120" />
              <mxPoint x="170" y="1913" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-96" value="其他小区b(仅输出一条结果，楼宇相关信息置默认值)" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="251.67000000000007" y="-150.94000000000005" width="123.75" height="61.88" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-98" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-102" target="dljHi8mAp1HHXRXAOkaB-104">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-99" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" vertex="1" connectable="0" parent="dljHi8mAp1HHXRXAOkaB-98">
          <mxGeometry x="0.0229" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-100" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-102" target="dljHi8mAp1HHXRXAOkaB-109">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-101" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" vertex="1" connectable="0" parent="dljHi8mAp1HHXRXAOkaB-100">
          <mxGeometry x="-0.0936" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-102" value="&lt;div&gt;小区对应的结果&lt;/div&gt;&lt;div&gt;记录条数小于等于5&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="505.88" y="1553" width="198.08" height="80" as="geometry" />
        </mxCell>
        <mxCell id="r5KGS2CTENY4qoHPWnlz-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-104" target="dljHi8mAp1HHXRXAOkaB-89">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="170" y="1593" />
              <mxPoint x="170" y="1913" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-104" value="怀疑有误a(小区对应的所有楼宇结果)" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="247.66999999999996" y="1563" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-105" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-109" target="dljHi8mAp1HHXRXAOkaB-111">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-106" value="NO" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" vertex="1" connectable="0" parent="dljHi8mAp1HHXRXAOkaB-105">
          <mxGeometry x="-0.0127" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-107" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-109" target="dljHi8mAp1HHXRXAOkaB-89">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-108" value="YES" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF0000;" vertex="1" connectable="0" parent="dljHi8mAp1HHXRXAOkaB-107">
          <mxGeometry x="-0.1955" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-109" value="小区落到&lt;font color=&quot;#ff0000&quot;&gt;主覆盖&lt;/font&gt;楼宇的采样点&lt;br&gt;占小区总采样点比例大于等于20%" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="478.86" y="1713" width="252.12" height="110" as="geometry" />
        </mxCell>
        <mxCell id="r5KGS2CTENY4qoHPWnlz-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-111" target="dljHi8mAp1HHXRXAOkaB-89">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="170" y="1768" />
              <mxPoint x="170" y="1913" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-111" value="怀疑有误b(小区对应的所有楼宇结果)" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="253.41999999999996" y="1738" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-112" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-113" target="dljHi8mAp1HHXRXAOkaB-118">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-113" value="mdt采样点(天粒度)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="1190" y="-590" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-114" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-115" target="dljHi8mAp1HHXRXAOkaB-118">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-115" value="电联4G非室分小区&amp;amp;&lt;br&gt;地铁小区" style="whiteSpace=wrap;html=1;rounded=0;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="1360" y="-490" width="115.83" height="30" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-116" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-118" target="dljHi8mAp1HHXRXAOkaB-123">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-117" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-118" target="dljHi8mAp1HHXRXAOkaB-126">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-118" value="(参与图层关联的采样点)经纬度+eci" style="whiteSpace=wrap;html=1;rounded=0;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="1190" y="-440" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-119" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-120" target="dljHi8mAp1HHXRXAOkaB-123">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-120" value="楼宇图层" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="1040" y="-370" width="110" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-121" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-123" target="dljHi8mAp1HHXRXAOkaB-125">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-122" value="楼宇id+eci汇聚" style="edgeLabel;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="dljHi8mAp1HHXRXAOkaB-121">
          <mxGeometry x="-0.1406" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-123" value="楼宇id+eci" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="1190" y="-290" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-124" value="过滤经纬度异常以及非室分小区和地铁小区" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="1249.9988888888888" y="-500" as="geometry" />
        </mxCell>
        <mxCell id="2QlyW9KXMpESUc1ni_Ah-0" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-125" target="dljHi8mAp1HHXRXAOkaB-4">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="970" y="-140" />
              <mxPoint x="970" y="-500" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="2QlyW9KXMpESUc1ni_Ah-2" value="楼宇id+eci汇聚" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="2QlyW9KXMpESUc1ni_Ah-0">
          <mxGeometry x="0.8017" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-125" value="楼宇id+eci+采样点数（天粒度）" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="1190" y="-170" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="2QlyW9KXMpESUc1ni_Ah-14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="dljHi8mAp1HHXRXAOkaB-126" target="2QlyW9KXMpESUc1ni_Ah-13">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1431" y="40" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-126" value="eci+采样点数&lt;br&gt;（天粒度）" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="1371" y="-170" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dljHi8mAp1HHXRXAOkaB-127" value="eci汇聚" style="edgeLabel;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="1431" y="-200" as="geometry" />
        </mxCell>
        <mxCell id="2QlyW9KXMpESUc1ni_Ah-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="2QlyW9KXMpESUc1ni_Ah-13" target="dljHi8mAp1HHXRXAOkaB-62">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="2QlyW9KXMpESUc1ni_Ah-13" value="eci+采样点数（月粒度）" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="1090" y="10" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="r5KGS2CTENY4qoHPWnlz-0" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="dljHi8mAp1HHXRXAOkaB-1" source="nKfmtWc3w1CRwPPpbfe2-0" target="dljHi8mAp1HHXRXAOkaB-23">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="nKfmtWc3w1CRwPPpbfe2-0" value="上两期均为&lt;span style=&quot;color: rgb(51, 153, 255);&quot;&gt;保留次覆盖楼宇&lt;/span&gt;的楼宇列表" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="dljHi8mAp1HHXRXAOkaB-1">
          <mxGeometry x="255.42000000000002" y="1233" width="120" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
