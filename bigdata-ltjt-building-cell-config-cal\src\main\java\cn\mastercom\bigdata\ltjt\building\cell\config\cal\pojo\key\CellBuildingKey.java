package cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.key;

import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.data.CellAndBuilding;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.res.BuildingCellConfig;
import scala.Serializable;

import java.util.Objects;

public class CellBuildingKey implements Serializable {

    private final Integer cityId;
    private final Long eci;
    private final Integer buildingId;

    public CellBuildingKey(BuildingCellConfig buildingCellConfig) {
        this.cityId = buildingCellConfig.getCityId();
        this.eci = buildingCellConfig.getEci();
        this.buildingId = buildingCellConfig.getBuildingId();
    }

    public CellBuildingKey(CellAndBuilding cellAndBuilding) {
        this.cityId = cellAndBuilding.getBuildingFeature().getCityId();
        this.eci = cellAndBuilding.getEci();
        this.buildingId = cellAndBuilding.getBuildingFeature().getId();
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof CellBuildingKey)) {
            return false;
        }

        CellBuildingKey cellBuildingKey = (CellBuildingKey) obj;
        return cityId.equals(cellBuildingKey.getCityId()) &&
                buildingId.equals(cellBuildingKey.getBuildingId()) &&
                eci.equals(cellBuildingKey.getEci());
    }

    @Override
    public int hashCode() {
        return Objects.hash(cityId, buildingId, eci);
    }

    public Integer getCityId() {
        return cityId;
    }

    public Long getEci() {
        return eci;
    }

    public Integer getBuildingId() {
        return buildingId;
    }

    @Override
    public String toString() {
        return cityId + "-" + buildingId + "-" + eci;
    }
}
