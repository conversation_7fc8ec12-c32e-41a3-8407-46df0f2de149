package cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils;

import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.storage.StorageLevel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import scala.Serializable;

public class SourceUtils implements Serializable {
    private static final Logger logger = LoggerFactory.getLogger(SourceUtils.class);

    private SourceUtils() {
        throw new IllegalStateException("Utility class");
    }

    public static JavaRDD<String> read(JavaSparkContext jsc, String path, String dataName) {
        if (null == path || path.trim().isEmpty()) {
            logger.info("=== Path cannot be null or empty ===");
            return null;
        }

        try {
            JavaRDD<String> rawDataRDD = jsc.textFile(path);
            rawDataRDD.persist(StorageLevel.MEMORY_AND_DISK());

            long count = rawDataRDD.count();
            if (count == 0) {
                logger.warn("=== No data found in the specified path: {} ===", path);
                return null;
            }

            logger.info("=== Total get {} {} data From {} ===", count, dataName, path);
            return rawDataRDD;
        } catch (Exception e) {
            logger.error("=== Failed to read file from path: {} ===", path, e);
            return null;
        }
    }

    public static JavaRDD<Row> read(SparkSession sparkSession, String sql, String dataName) {
        if (null == sql || sql.trim().isEmpty()) {
            logger.info("=== Sql cannot be null or empty ===");
            return null;
        }

        try {
            JavaRDD<Row> rawDataRDD = sparkSession.sql(sql).toJavaRDD();
            rawDataRDD.persist(StorageLevel.MEMORY_AND_DISK());

            long count = rawDataRDD.count();
            if (count == 0) {
                logger.warn("=== No data found with sql: {} ===", sql);
                return null;
            }

            logger.info("=== Total get {} {} data From sql: {} ===", count, dataName, sql);
            return rawDataRDD;
        } catch (Exception e) {
            logger.error("=== Failed to execute sql: {} ===", sql, e);
            return null;
        }
    }
}
