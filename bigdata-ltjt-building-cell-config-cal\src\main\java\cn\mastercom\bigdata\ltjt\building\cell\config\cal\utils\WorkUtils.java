package cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils;

import cn.mastercom.mtcommon.gis.GisConverter;
import org.apache.commons.lang.StringUtils;
import scala.Serializable;

public class WorkUtils implements Serializable {
    public static final int INVALID_INTEGER_VALUE = -1000000;
    public static final int GREAT_RSRP_VALUE = -110;
    public static final int INVALID_ID = -1;
    public static final Double INVALID_DOUBLE_VALUE = -1000000.0d;

    private WorkUtils() {
        throw new IllegalStateException("Utility class");
    }

    public static int stringToInt(String value, int defaultValue) {
        try {
            if (value.isEmpty() || value.equalsIgnoreCase("null")) {
                return defaultValue;
            }
            return (int) Double.parseDouble(value);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static boolean stringToBoolean(String value, boolean defaultValue) {
        try {
            if (value.isEmpty() || value.equalsIgnoreCase("null")) {
                return defaultValue;
            }
            return Boolean.parseBoolean(value);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static double stringToDouble(String value, double defaultValue) {
        try {
            if (value.isEmpty() || value.equalsIgnoreCase("null")) {
                return defaultValue;
            }
            return Double.parseDouble(value);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static long stringToLong(String value, long defaultValue) {
        try {
            if (value.isEmpty() || value.equalsIgnoreCase("null")) {
                return defaultValue;
            }
            return Long.parseLong(value);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static double getRsrp(String value) {
        if (StringUtils.isEmpty(value) || value.equals(String.valueOf(-1000000))) {
            return 0.0d;
        }
        return Double.parseDouble(value);
    }

    public static boolean isPositionValid(double longitude, double latitude) {
        return longitude > 0.0d && longitude < 180.0d && latitude > 0.0d && latitude < 90.0d;
    }

    public static boolean isPositionValid(int longitude, int latitude) {
        return isPositionValid(GisConverter.i2d(longitude), GisConverter.i2d(latitude));
    }

    public static void main(String[] args) {
        System.out.println(isPositionValid(112.1, 50.2));
    }

}