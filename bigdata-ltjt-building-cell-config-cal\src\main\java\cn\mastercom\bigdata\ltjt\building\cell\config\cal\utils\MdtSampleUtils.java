package cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils;

import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.ProvinceEnum;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.data.MdtSample;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.key.CellKey;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.api.java.function.Function;
import org.apache.spark.api.java.function.Function2;
import org.apache.spark.api.java.function.PairFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import scala.Serializable;
import scala.Tuple2;

import java.time.LocalDate;

public class MdtSampleUtils implements Serializable {
    private static final Logger logger = LoggerFactory.getLogger(MdtSampleUtils.class);

    private MdtSampleUtils() {
        throw new IllegalStateException("Utility class");
    }

    public static JavaRDD<MdtSample> readMdtSample(JavaSparkContext jsc, ProvinceEnum workProvince, LocalDate taskDate) {
        String mdtDataPath = LtjtPathUtils.getMdtDataPath(workProvince, taskDate);
        JavaRDD<String> rawDataRDD = SourceUtils.read(jsc, mdtDataPath, DateUtils.transDateToString(taskDate, "yyMMdd") + "-mdt");
        if (null == rawDataRDD) {
            String taskDateStr = DateUtils.transDateToString(taskDate, "yyyy-MM-dd");
            logger.info("=== {} mdt data path {} do not exists, quit ===", taskDateStr, mdtDataPath);
            return null;
        }
        return rawDataRDD
                .map((Function<String, MdtSample>) MdtSample::new);
    }

    public static void statCellTotalMrCnt(JavaRDD<MdtSample> mdtSampleRDD, ProvinceEnum workProvince, LocalDate taskDate) {
        // 分小区统计采样点数，并且输出
        String cellAndMrCntOutputPath = LtjtPathUtils
                .getOutputPath("cell-total-mrcnt", taskDate, workProvince);
        if (HdfsUtil.prepareForOutput(cellAndMrCntOutputPath)) {
            mdtSampleRDD
                    .mapToPair((PairFunction<MdtSample, CellKey, Long>) mdtSample ->
                            new Tuple2<>(new CellKey(mdtSample), 1L))
                    .reduceByKey((Function2<Long, Long, Long>) (v1, v2) -> {
                        v1 += v2;
                        return v1;
                    })
                    .map((Function<Tuple2<CellKey, Long>, String>) v1 ->
                            v1._1().getEci() + "\t" + v1._2())
                    .repartition(2)
                    .saveAsTextFile(cellAndMrCntOutputPath);
        }
    }
}
