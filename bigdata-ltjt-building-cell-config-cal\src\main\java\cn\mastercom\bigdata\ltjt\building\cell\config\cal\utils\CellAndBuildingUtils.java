package cn.mastercom.bigdata.ltjt.building.cell.config.cal.utils;

import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.ProvinceEnum;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.cell.CellConfig;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.data.CellAndBuilding;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.feature.BuildingFeature;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.key.BuildingKey;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.key.CellBuildingKey;
import cn.mastercom.bigdata.ltjt.building.cell.config.cal.pojo.key.CellKey;
import org.apache.spark.api.java.JavaPairRDD;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.api.java.function.Function;
import org.apache.spark.api.java.function.Function2;
import org.apache.spark.api.java.function.PairFunction;
import org.apache.spark.broadcast.Broadcast;
import org.apache.spark.sql.SparkSession;
import scala.Serializable;
import scala.Tuple2;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public class CellAndBuildingUtils implements Serializable {

    private CellAndBuildingUtils() {
        throw new IllegalStateException("Utility class");
    }

    public static JavaRDD<CellAndBuilding> readCellAndBuildingFromHdfs(SparkSession sparkSession, JavaSparkContext jsc,
                                                                       String taskMonth, ProvinceEnum workProvince,
                                                                       Broadcast<List<CellConfig>> indoorCellConfigListBroadcast) {
        Map<CellKey, CellConfig> cellConfigMap = CellUtils.getEciCellConfigMap(indoorCellConfigListBroadcast.value());
        JavaPairRDD<BuildingKey, BuildingFeature> buildingFeaturePairRDD = LayerUtils
                .getBuildingFeaturePairRDD(sparkSession, workProvince);

        String dataPath = LtjtPathUtils.getOutputPath("cell-and-building", taskMonth, workProvince);
        JavaRDD<String> rawDataRDD = SourceUtils.read(jsc, dataPath, "monthly-cell-and-building");
        if (rawDataRDD == null) {
            return null;
        }

        return rawDataRDD
                .mapToPair((PairFunction<String, BuildingKey, CellAndBuilding>) line -> {
                    int index = 0;
                    String[] itemList = line.split("\t");
                    int cityId = WorkUtils.stringToInt(itemList[index++], -1);
                    Long eci = WorkUtils.stringToLong(itemList[index++], -1L);
                    int buildingId = WorkUtils.stringToInt(itemList[index++], -1);
                    long mrCnt = WorkUtils.stringToLong(itemList[index], -1L);

                    CellKey cellKey = new CellKey(eci);
                    BuildingKey buildingKey = new BuildingKey(cityId, buildingId);

                    if (!cellConfigMap.containsKey(cellKey)) {
                        return null;
                    }
                    return new Tuple2<>(buildingKey, new CellAndBuilding(cellConfigMap.get(cellKey), null, mrCnt));
                })
                .filter((Function<Tuple2<BuildingKey, CellAndBuilding>, Boolean>) Objects::nonNull)
                .join(buildingFeaturePairRDD)
                .mapToPair((PairFunction<Tuple2<BuildingKey, Tuple2<CellAndBuilding, BuildingFeature>>, CellBuildingKey, CellAndBuilding>) v1 -> {
                    CellAndBuilding cellAndBuilding = v1._2()._1();
                    BuildingFeature buildingFeature = v1._2()._2();
                    cellAndBuilding.setBuildingFeature(buildingFeature);
                    return new Tuple2<>(new CellBuildingKey(cellAndBuilding), cellAndBuilding);
                })
                .reduceByKey((Function2<CellAndBuilding, CellAndBuilding, CellAndBuilding>) (v1, v2) -> {
                    v1.merge(v2);
                    return v1;
                })
                .values();
    }
}
